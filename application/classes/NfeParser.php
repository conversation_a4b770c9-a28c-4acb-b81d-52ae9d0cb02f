<?php

class NfeParser
{
    /**
     * Converte o XML da NFe em array PHP estruturado
     * 
     * @param string $xmlPath Caminho para o arquivo XML
     * @return array Array estruturado com os dados da NFe
     */
    public static function parseXmlToArray($xmlPath)
    {
        // Carrega o XML
        $xmlContent = file_get_contents($xmlPath);
        
        // Remove namespace para simplificar o parse
        $xmlContent = str_replace('xmlns="http://www.portalfiscal.inf.br/nfe"', '', $xmlContent);
        
        // Converte XML para SimpleXMLElement
        $xml = simplexml_load_string($xmlContent);
        
        if (!$xml) {
            throw new Exception("Erro ao processar o XML da NFe");
        }
        
        $nfe = $xml->infNFe;
        
        // Array estruturado da NFe
        $nfeArray = [
            'identificacao' => [
                'id' => (string) $xml->infNFe['Id'],
                'versao' => (string) $xml->infNFe['versao'],
                'chave_acesso' => str_replace('NFe', '', (string) $xml->infNFe['Id']),
                'codigo_uf' => (string) $nfe->ide->cUF,
                'codigo_nf' => (string) $nfe->ide->cNF,
                'natureza_operacao' => (string) $nfe->ide->natOp,
                'modelo' => (string) $nfe->ide->mod,
                'serie' => (string) $nfe->ide->serie,
                'numero' => (string) $nfe->ide->nNF,
                'data_emissao' => (string) $nfe->ide->dhEmi,
                'data_saida' => (string) $nfe->ide->dhSaiEnt,
                'tipo_nf' => (string) $nfe->ide->tpNF,
                'destino' => (string) $nfe->ide->idDest,
                'municipio_fg' => (string) $nfe->ide->cMunFG,
                'tipo_impressao' => (string) $nfe->ide->tpImp,
                'tipo_emissao' => (string) $nfe->ide->tpEmis,
                'digito_verificador' => (string) $nfe->ide->cDV,
                'ambiente' => (string) $nfe->ide->tpAmb,
                'finalidade' => (string) $nfe->ide->finNFe,
                'consumidor_final' => (string) $nfe->ide->indFinal,
                'presenca' => (string) $nfe->ide->indPres,
                'processo_emissao' => (string) $nfe->ide->procEmi,
                'versao_processo' => (string) $nfe->ide->verProc
            ],
            
            'emitente' => [
                'cnpj' => (string) $nfe->emit->CNPJ,
                'razao_social' => (string) $nfe->emit->xNome,
                'nome_fantasia' => (string) $nfe->emit->xFant,
                'endereco' => [
                    'logradouro' => (string) $nfe->emit->enderEmit->xLgr,
                    'numero' => (string) $nfe->emit->enderEmit->nro,
                    'bairro' => (string) $nfe->emit->enderEmit->xBairro,
                    'codigo_municipio' => (string) $nfe->emit->enderEmit->cMun,
                    'municipio' => (string) $nfe->emit->enderEmit->xMun,
                    'uf' => (string) $nfe->emit->enderEmit->UF,
                    'cep' => (string) $nfe->emit->enderEmit->CEP,
                    'codigo_pais' => (string) $nfe->emit->enderEmit->cPais,
                    'pais' => (string) $nfe->emit->enderEmit->xPais,
                    'telefone' => (string) $nfe->emit->enderEmit->fone
                ],
                'inscricao_estadual' => (string) $nfe->emit->IE,
                'inscricao_municipal' => (string) $nfe->emit->IM,
                'cnae' => (string) $nfe->emit->CNAE,
                'regime_tributario' => (string) $nfe->emit->CRT
            ],
            
            'destinatario' => [
                'id_estrangeiro' => (string) $nfe->dest->idEstrangeiro,
                'razao_social' => (string) $nfe->dest->xNome,
                'endereco' => [
                    'logradouro' => (string) $nfe->dest->enderDest->xLgr,
                    'numero' => (string) $nfe->dest->enderDest->nro,
                    'complemento' => (string) $nfe->dest->enderDest->xCpl,
                    'bairro' => (string) $nfe->dest->enderDest->xBairro,
                    'codigo_municipio' => (string) $nfe->dest->enderDest->cMun,
                    'municipio' => (string) $nfe->dest->enderDest->xMun,
                    'uf' => (string) $nfe->dest->enderDest->UF,
                    'cep' => (string) $nfe->dest->enderDest->CEP,
                    'codigo_pais' => (string) $nfe->dest->enderDest->cPais,
                    'pais' => (string) $nfe->dest->enderDest->xPais
                ],
                'indicador_ie' => (string) $nfe->dest->indIEDest
            ],
            
            'itens' => [],
            
            'totais' => [
                'base_calculo_icms' => (float) $nfe->total->ICMSTot->vBC,
                'valor_icms' => (float) $nfe->total->ICMSTot->vICMS,
                'valor_produtos' => (float) $nfe->total->ICMSTot->vProd,
                'valor_frete' => (float) $nfe->total->ICMSTot->vFrete,
                'valor_seguro' => (float) $nfe->total->ICMSTot->vSeg,
                'valor_desconto' => (float) $nfe->total->ICMSTot->vDesc,
                'valor_ii' => (float) $nfe->total->ICMSTot->vII,
                'valor_ipi' => (float) $nfe->total->ICMSTot->vIPI,
                'valor_pis' => (float) $nfe->total->ICMSTot->vPIS,
                'valor_cofins' => (float) $nfe->total->ICMSTot->vCOFINS,
                'valor_outras_despesas' => (float) $nfe->total->ICMSTot->vOutro,
                'valor_total_nf' => (float) $nfe->total->ICMSTot->vNF,
                'valor_total_tributos' => (float) $nfe->total->ICMSTot->vTotTrib
            ],
            
            'transporte' => [
                'modalidade_frete' => (string) $nfe->transp->modFrete,
                'transportadora' => [
                    'cnpj' => (string) $nfe->transp->transporta->CNPJ,
                    'razao_social' => (string) $nfe->transp->transporta->xNome,
                    'endereco' => (string) $nfe->transp->transporta->xEnder,
                    'municipio' => (string) $nfe->transp->transporta->xMun,
                    'uf' => (string) $nfe->transp->transporta->UF
                ],
                'volumes' => [
                    'quantidade' => (int) $nfe->transp->vol->qVol,
                    'especie' => (string) $nfe->transp->vol->esp,
                    'peso_liquido' => (float) $nfe->transp->vol->pesoL,
                    'peso_bruto' => (float) $nfe->transp->vol->pesoB
                ]
            ],
            
            'pagamento' => [
                'tipo_pagamento' => (string) $nfe->pag->detPag->tPag,
                'descricao_pagamento' => (string) $nfe->pag->detPag->xPag,
                'valor_pagamento' => (float) $nfe->pag->detPag->vPag
            ]
        ];
        
        // Processa os itens da NFe
        if (isset($nfe->det)) {
            foreach ($nfe->det as $det) {
                $item = [
                    'numero_item' => (string) $det['nItem'],
                    'produto' => [
                        'codigo' => (string) $det->prod->cProd,
                        'ean' => (string) $det->prod->cEAN,
                        'descricao' => (string) $det->prod->xProd,
                        'ncm' => (string) $det->prod->NCM,
                        'cfop' => (string) $det->prod->CFOP,
                        'unidade_comercial' => (string) $det->prod->uCom,
                        'quantidade_comercial' => (float) $det->prod->qCom,
                        'valor_unitario_comercial' => (float) $det->prod->vUnCom,
                        'valor_produto' => (float) $det->prod->vProd,
                        'ean_tributavel' => (string) $det->prod->cEANTrib,
                        'unidade_tributavel' => (string) $det->prod->uTrib,
                        'quantidade_tributavel' => (float) $det->prod->qTrib,
                        'valor_unitario_tributavel' => (float) $det->prod->vUnTrib,
                        'valor_frete' => (float) $det->prod->vFrete,
                        'valor_seguro' => (float) $det->prod->vSeg,
                        'compoe_total' => (string) $det->prod->indTot,
                        'pedido' => (string) $det->prod->xPed,
                        'item_pedido' => (string) $det->prod->nItemPed,
                        'informacoes_adicionais' => (string) $det->prod->infAdProd
                    ]
                ];
                
                // Adiciona informações de DI (Declaração de Importação) se existir
                if (isset($det->prod->DI)) {
                    $item['produto']['declaracao_importacao'] = [
                        'numero_di' => (string) $det->prod->DI->nDI,
                        'data_di' => (string) $det->prod->DI->dDI,
                        'local_desembarque' => (string) $det->prod->DI->xLocDesemb,
                        'uf_desembarque' => (string) $det->prod->DI->UFDesemb,
                        'data_desembarque' => (string) $det->prod->DI->dDesemb,
                        'tipo_via_transporte' => (string) $det->prod->DI->tpViaTransp,
                        'valor_afrmm' => (float) $det->prod->DI->vAFRMM,
                        'tipo_intermediacao' => (string) $det->prod->DI->tpIntermedio,
                        'codigo_exportador' => (string) $det->prod->DI->cExportador,
                        'adicoes' => [
                            'numero_adicao' => (string) $det->prod->DI->adi->nAdicao,
                            'sequencia_adicao' => (string) $det->prod->DI->adi->nSeqAdic,
                            'codigo_fabricante' => (string) $det->prod->DI->adi->cFabricante
                        ]
                    ];
                }
                
                // Adiciona informações de impostos
                if (isset($det->imposto)) {
                    $item['impostos'] = [];
                    
                    // ICMS
                    if (isset($det->imposto->ICMS->ICMS00)) {
                        $item['impostos']['icms'] = [
                            'origem' => (string) $det->imposto->ICMS->ICMS00->orig,
                            'cst' => (string) $det->imposto->ICMS->ICMS00->CST,
                            'modalidade_bc' => (string) $det->imposto->ICMS->ICMS00->modBC,
                            'base_calculo' => (float) $det->imposto->ICMS->ICMS00->vBC,
                            'aliquota' => (float) $det->imposto->ICMS->ICMS00->pICMS,
                            'valor' => (float) $det->imposto->ICMS->ICMS00->vICMS
                        ];
                    }
                    
                    // IPI
                    if (isset($det->imposto->IPI->IPITrib)) {
                        $item['impostos']['ipi'] = [
                            'codigo_enquadramento' => (string) $det->imposto->IPI->cEnq,
                            'cst' => (string) $det->imposto->IPI->IPITrib->CST,
                            'base_calculo' => (float) $det->imposto->IPI->IPITrib->vBC,
                            'aliquota' => (float) $det->imposto->IPI->IPITrib->pIPI,
                            'valor' => (float) $det->imposto->IPI->IPITrib->vIPI
                        ];
                    }
                    
                    // II (Imposto de Importação)
                    if (isset($det->imposto->II)) {
                        $item['impostos']['ii'] = [
                            'base_calculo' => (float) $det->imposto->II->vBC,
                            'despesas_aduaneiras' => (float) $det->imposto->II->vDespAdu,
                            'valor' => (float) $det->imposto->II->vII,
                            'valor_iof' => (float) $det->imposto->II->vIOF
                        ];
                    }
                    
                    // PIS
                    if (isset($det->imposto->PIS->PISAliq)) {
                        $item['impostos']['pis'] = [
                            'cst' => (string) $det->imposto->PIS->PISAliq->CST,
                            'base_calculo' => (float) $det->imposto->PIS->PISAliq->vBC,
                            'aliquota' => (float) $det->imposto->PIS->PISAliq->pPIS,
                            'valor' => (float) $det->imposto->PIS->PISAliq->vPIS
                        ];
                    }
                    
                    // COFINS
                    if (isset($det->imposto->COFINS->COFINSAliq)) {
                        $item['impostos']['cofins'] = [
                            'cst' => (string) $det->imposto->COFINS->COFINSAliq->CST,
                            'base_calculo' => (float) $det->imposto->COFINS->COFINSAliq->vBC,
                            'aliquota' => (float) $det->imposto->COFINS->COFINSAliq->pCOFINS,
                            'valor' => (float) $det->imposto->COFINS->COFINSAliq->vCOFINS
                        ];
                    }
                }
                
                $nfeArray['itens'][] = $item;
            }
        }
        
        return $nfeArray;
    }
    
    /**
     * Exemplo de uso da classe
     */
    public static function exemploUso()
    {
        $xmlPath = '/home/<USER>/environment/Office/Apps/inProduction/metrics/application/views/42250701103171000613550010000006051070856197-nfe.xml';
        
        try {
            $nfeArray = self::parseXmlToArray($xmlPath);
            
            // Exibe algumas informações básicas
            echo "=== INFORMAÇÕES DA NFE ===\n";
            echo "Chave de Acesso: " . $nfeArray['identificacao']['chave_acesso'] . "\n";
            echo "Número: " . $nfeArray['identificacao']['numero'] . "\n";
            echo "Série: " . $nfeArray['identificacao']['serie'] . "\n";
            echo "Data Emissão: " . $nfeArray['identificacao']['data_emissao'] . "\n";
            echo "Valor Total: R$ " . number_format($nfeArray['totais']['valor_total_nf'], 2, ',', '.') . "\n";
            echo "\n";
            
            echo "=== EMITENTE ===\n";
            echo "CNPJ: " . $nfeArray['emitente']['cnpj'] . "\n";
            echo "Razão Social: " . $nfeArray['emitente']['razao_social'] . "\n";
            echo "Nome Fantasia: " . $nfeArray['emitente']['nome_fantasia'] . "\n";
            echo "\n";
            
            echo "=== DESTINATÁRIO ===\n";
            echo "Razão Social: " . $nfeArray['destinatario']['razao_social'] . "\n";
            echo "País: " . $nfeArray['destinatario']['endereco']['pais'] . "\n";
            echo "\n";
            
            echo "=== ITENS ===\n";
            echo "Total de Itens: " . count($nfeArray['itens']) . "\n";
            
            foreach ($nfeArray['itens'] as $index => $item) {
                if ($index < 3) { // Mostra apenas os 3 primeiros itens
                    echo "\nItem " . ($index + 1) . ":\n";
                    echo "  Código: " . $item['produto']['codigo'] . "\n";
                    echo "  Descrição: " . substr($item['produto']['descricao'], 0, 50) . "...\n";
                    echo "  Quantidade: " . $item['produto']['quantidade_comercial'] . "\n";
                    echo "  Valor Unit.: R$ " . number_format($item['produto']['valor_unitario_comercial'], 2, ',', '.') . "\n";
                    echo "  Valor Total: R$ " . number_format($item['produto']['valor_produto'], 2, ',', '.') . "\n";
                }
            }
            
            echo "\n=== ARRAY COMPLETO ===\n";
            echo "Para ver o array completo, use: print_r(\$nfeArray) ou var_dump(\$nfeArray)\n";
            
            return $nfeArray;
            
        } catch (Exception $e) {
            echo "Erro ao processar NFe: " . $e->getMessage() . "\n";
            return false;
        }
    }
}

// Exemplo de uso
if (php_sapi_name() === 'cli') {
    // Se executado via linha de comando
    NfeParser::exemploUso();
}
