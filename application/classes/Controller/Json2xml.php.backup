<?php defined('SYSPATH') or die('No direct script access.');

/**
 * Controller Json2xml
 * Converte estrutura JSON de procNfe de volta para XML
 */
class Controller_Json2xml extends Controller_Websession {

    /**
     * Ação principal - converte JSON para XML
     */
    public function action_index() {
        try {
            // Log para debug
            error_log("Json2xml controller - REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD']);
            
            // Verifica se foi enviado um arquivo JSON via POST
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                error_log("Json2xml controller - POST recebido");
                
                if (isset($_FILES['json_file']) && $_FILES['json_file']['error'] === UPLOAD_ERR_OK) {
                    error_log("Json2xml controller - Arquivo uploaded");
                    $jsonContent = file_get_contents($_FILES['json_file']['tmp_name']);
                    $this->processJsonToXml($jsonContent, $_FILES['json_file']['name']);
                } elseif (isset($_POST['json_content']) && !empty($_POST['json_content'])) {
                    error_log("Json2xml controller - JSON content postado");
                    $this->processJsonToXml($_POST['json_content'], 'converted');
                } else {
                    error_log("Json2xml controller - Nenhum arquivo ou conteúdo");
                    throw new Exception('Nenhum arquivo JSON ou conteúdo fornecido');
                }
            } else {
                // Exibe o formulário
                error_log("Json2xml controller - Exibindo formulário");
                $this->showForm();
            }
        } catch (Exception $e) {
            error_log("Json2xml controller - Erro: " . $e->getMessage());
            $this->showError($e->getMessage());
        }
    }

    /**
     * Processa a conversão JSON para XML
     */
    private function processJsonToXml($jsonContent, $filename) {
        try {
            error_log("Json2xml - Iniciando processamento. Tamanho JSON: " . strlen($jsonContent));
            
            // Decodifica o JSON
            $data = json_decode($jsonContent, true);
            
            if ($data === null) {
                $error = 'JSON inválido: ' . json_last_error_msg();
                error_log("Json2xml - Erro JSON: " . $error);
                throw new Exception($error);
            }

            error_log("Json2xml - JSON decodificado com sucesso");

            // Valida estrutura básica
            if (!isset($data['nfe']) || !isset($data['protocolo'])) {
                $error = 'Estrutura JSON inválida. Deve conter "nfe" e "protocolo"';
                error_log("Json2xml - Erro estrutura: " . $error);
                throw new Exception($error);
            }

            error_log("Json2xml - Estrutura validada");

            // Converte para XML
            $xml = $this->convertToXml($data);
            
            error_log("Json2xml - XML gerado. Tamanho: " . strlen($xml));

            // Define o nome do arquivo de saída
            $outputFilename = pathinfo($filename, PATHINFO_FILENAME) . '_converted.xml';

            // Envia o arquivo XML para download
            header('Content-Type: application/xml; charset=UTF-8');
            header('Content-Disposition: attachment; filename="' . $outputFilename . '"');
            header('Content-Length: ' . strlen($xml));
            header('Cache-Control: no-cache, must-revalidate');
            header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
            
            // Limpa qualquer output buffer
            if (ob_get_level()) {
                ob_end_clean();
            }
            
            echo $xml;
            exit;
            
        } catch (Exception $e) {
            error_log("Json2xml - Erro no processamento: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Converte array PHP para XML procNfe
     */
    private function convertToXml($data) {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        
        // Namespace do procNfe
        $xml .= '<nfeProc versao="' . $data['versao'] . '" xmlns="http://www.portalfiscal.inf.br/nfe">' . "\n";
        
        // Adiciona a NFe
        $xml .= $this->buildNfeXml($data['nfe']);
        
        // Adiciona o protocolo
        $xml .= $this->buildProtocoloXml($data['protocolo']);
        
        $xml .= '</nfeProc>';
        
        return $xml;
    }

    /**
     * Constrói o XML da NFe
     */
    private function buildNfeXml($nfe) {
        $xml = '  <NFe xmlns="http://www.portalfiscal.inf.br/nfe">' . "\n";
        
        // infNFe
        $xml .= '    <infNFe Id="' . $nfe['id'] . '" versao="' . $nfe['versao'] . '">' . "\n";
        
        // Identificação
        if (isset($nfe['identificacao'])) {
            $xml .= $this->buildIdeXml($nfe['identificacao']);
        }
        
        // Emitente
        if (isset($nfe['emitente'])) {
            $xml .= $this->buildEmitXml($nfe['emitente']);
        }
        
        // Destinatário
        if (isset($nfe['destinatario'])) {
            $xml .= $this->buildDestXml($nfe['destinatario']);
        }
        
        // Itens
        if (isset($nfe['itens'])) {
            $xml .= $this->buildItensXml($nfe['itens']);
        }
        
        // Total
        if (isset($nfe['total'])) {
            $xml .= $this->buildTotalXml($nfe['total']);
        }
        
        // Transporte
        if (isset($nfe['transporte'])) {
            $xml .= $this->buildTranspXml($nfe['transporte']);
        }
        
        // Pagamento
        if (isset($nfe['pagamento'])) {
            $xml .= $this->buildPagXml($nfe['pagamento']);
        }
        
        // Informações adicionais
        if (isset($nfe['informacoes_adicionais'])) {
            $xml .= $this->buildInfAdicXml($nfe['informacoes_adicionais']);
        }
        
        // Responsável técnico
        if (isset($nfe['responsavel_tecnico'])) {
            $xml .= $this->buildInfRespTecXml($nfe['responsavel_tecnico']);
        }
        
        $xml .= '    </infNFe>' . "\n";
        
        // Assinatura
        if (isset($nfe['assinatura'])) {
            $xml .= $this->buildSignatureXml($nfe['assinatura']);
        }
        
        $xml .= '  </NFe>' . "\n";
        
        return $xml;
    }

    /**
     * Constrói XML da identificação
     */
    private function buildIdeXml($ide) {
        $xml = '      <ide>' . "\n";
        $xml .= '        <cUF>' . $ide['codigo_uf'] . '</cUF>' . "\n";
        $xml .= '        <cNF>' . $ide['codigo_nf'] . '</cNF>' . "\n";
        $xml .= '        <natOp>' . htmlspecialchars($ide['natureza_operacao']) . '</natOp>' . "\n";
        $xml .= '        <mod>' . $ide['modelo'] . '</mod>' . "\n";
        $xml .= '        <serie>' . $ide['serie'] . '</serie>' . "\n";
        $xml .= '        <nNF>' . $ide['numero_nf'] . '</nNF>' . "\n";
        $xml .= '        <dhEmi>' . $ide['data_emissao'] . '</dhEmi>' . "\n";
        $xml .= '        <dhSaiEnt>' . $ide['data_saida_entrada'] . '</dhSaiEnt>' . "\n";
        $xml .= '        <tpNF>' . $ide['tipo_nf'] . '</tpNF>' . "\n";
        $xml .= '        <idDest>' . $ide['identificacao_destino'] . '</idDest>' . "\n";
        $xml .= '        <cMunFG>' . $ide['codigo_municipio_fg'] . '</cMunFG>' . "\n";
        $xml .= '        <tpImp>' . $ide['tipo_impressao'] . '</tpImp>' . "\n";
        $xml .= '        <tpEmis>' . $ide['tipo_emissao'] . '</tpEmis>' . "\n";
        $xml .= '        <cDV>' . $ide['digito_verificador'] . '</cDV>' . "\n";
        $xml .= '        <tpAmb>' . $ide['tipo_ambiente'] . '</tpAmb>' . "\n";
        $xml .= '        <finNFe>' . $ide['finalidade_nfe'] . '</finNFe>' . "\n";
        $xml .= '        <indFinal>' . $ide['indicador_consumidor_final'] . '</indFinal>' . "\n";
        $xml .= '        <indPres>' . $ide['indicador_presenca'] . '</indPres>' . "\n";
        $xml .= '        <procEmi>' . $ide['processo_emissao'] . '</procEmi>' . "\n";
        $xml .= '        <verProc>' . htmlspecialchars($ide['versao_processo']) . '</verProc>' . "\n";
        $xml .= '      </ide>' . "\n";
        return $xml;
    }

    /**
     * Constrói XML do emitente
     */
    private function buildEmitXml($emit) {
        $xml = '      <emit>' . "\n";
        if (isset($emit['cnpj'])) {
            $xml .= '        <CNPJ>' . $emit['cnpj'] . '</CNPJ>' . "\n";
        }
        $xml .= '        <xNome>' . htmlspecialchars($emit['nome']) . '</xNome>' . "\n";
        if (isset($emit['fantasia'])) {
            $xml .= '        <xFant>' . htmlspecialchars($emit['fantasia']) . '</xFant>' . "\n";
        }
        
        // Endereço
        if (isset($emit['endereco'])) {
            $endereco = $emit['endereco'];
            $xml .= '        <enderEmit>' . "\n";
            $xml .= '          <xLgr>' . htmlspecialchars($endereco['logradouro']) . '</xLgr>' . "\n";
            $xml .= '          <nro>' . $endereco['numero'] . '</nro>' . "\n";
            $xml .= '          <xBairro>' . htmlspecialchars($endereco['bairro']) . '</xBairro>' . "\n";
            $xml .= '          <cMun>' . $endereco['codigo_municipio'] . '</cMun>' . "\n";
            $xml .= '          <xMun>' . htmlspecialchars($endereco['municipio']) . '</xMun>' . "\n";
            $xml .= '          <UF>' . $endereco['uf'] . '</UF>' . "\n";
            $xml .= '          <CEP>' . $endereco['cep'] . '</CEP>' . "\n";
            $xml .= '          <cPais>' . $endereco['codigo_pais'] . '</cPais>' . "\n";
            $xml .= '          <xPais>' . htmlspecialchars($endereco['pais']) . '</xPais>' . "\n";
            if (isset($endereco['telefone'])) {
                $xml .= '          <fone>' . $endereco['telefone'] . '</fone>' . "\n";
            }
            $xml .= '        </enderEmit>' . "\n";
        }
        
        if (isset($emit['inscricao_estadual'])) {
            $xml .= '        <IE>' . $emit['inscricao_estadual'] . '</IE>' . "\n";
        }
        if (isset($emit['inscricao_municipal'])) {
            $xml .= '        <IM>' . $emit['inscricao_municipal'] . '</IM>' . "\n";
        }
        if (isset($emit['cnae'])) {
            $xml .= '        <CNAE>' . $emit['cnae'] . '</CNAE>' . "\n";
        }
        if (isset($emit['crt'])) {
            $xml .= '        <CRT>' . $emit['crt'] . '</CRT>' . "\n";
        }
        
        $xml .= '      </emit>' . "\n";
        return $xml;
    }

    /**
     * Constrói XML do destinatário
     */
    private function buildDestXml($dest) {
        $xml = '      <dest>' . "\n";
        
        if (isset($dest['cnpj'])) {
            $xml .= '        <CNPJ>' . $dest['cnpj'] . '</CNPJ>' . "\n";
        } elseif (isset($dest['cpf'])) {
            $xml .= '        <CPF>' . $dest['cpf'] . '</CPF>' . "\n";
        } elseif (isset($dest['id_estrangeiro'])) {
            $xml .= '        <idEstrangeiro>' . $dest['id_estrangeiro'] . '</idEstrangeiro>' . "\n";
        }
        
        $xml .= '        <xNome>' . htmlspecialchars($dest['nome']) . '</xNome>' . "\n";
        
        // Endereço
        if (isset($dest['endereco'])) {
            $endereco = $dest['endereco'];
            $xml .= '        <enderDest>' . "\n";
            $xml .= '          <xLgr>' . htmlspecialchars($endereco['logradouro']) . '</xLgr>' . "\n";
            $xml .= '          <nro>' . $endereco['numero'] . '</nro>' . "\n";
            if (isset($endereco['complemento'])) {
                $xml .= '          <xCpl>' . htmlspecialchars($endereco['complemento']) . '</xCpl>' . "\n";
            }
            $xml .= '          <xBairro>' . htmlspecialchars($endereco['bairro']) . '</xBairro>' . "\n";
            $xml .= '          <cMun>' . $endereco['codigo_municipio'] . '</cMun>' . "\n";
            $xml .= '          <xMun>' . htmlspecialchars($endereco['municipio']) . '</xMun>' . "\n";
            $xml .= '          <UF>' . $endereco['uf'] . '</UF>' . "\n";
            if (isset($endereco['codigo_pais'])) {
                $xml .= '          <cPais>' . $endereco['codigo_pais'] . '</cPais>' . "\n";
            }
            if (isset($endereco['pais'])) {
                $xml .= '          <xPais>' . htmlspecialchars($endereco['pais']) . '</xPais>' . "\n";
            }
            $xml .= '        </enderDest>' . "\n";
        }
        
        if (isset($dest['indicador_ie'])) {
            $xml .= '        <indIEDest>' . $dest['indicador_ie'] . '</indIEDest>' . "\n";
        }
        
        $xml .= '      </dest>' . "\n";
        return $xml;
    }

    /**
     * Constrói XML dos itens
     */
    private function buildItensXml($itens) {
        $xml = '';
        
        foreach ($itens as $item) {
            $xml .= '      <det nItem="' . $item['numero_item'] . '">' . "\n";
            
            // Produto
            if (isset($item['produto'])) {
                $xml .= $this->buildProdXml($item['produto']);
            }
            
            // Impostos
            if (isset($item['impostos'])) {
                $xml .= $this->buildImpostosXml($item['impostos']);
            }
            
            $xml .= '      </det>' . "\n";
        }
        
        return $xml;
    }

    /**
     * Constrói XML do produto
     */
    private function buildProdXml($prod) {
        $xml = '        <prod>' . "\n";
        $xml .= '          <cProd>' . $prod['codigo'] . '</cProd>' . "\n";
        $xml .= '          <cEAN>' . $prod['ean'] . '</cEAN>' . "\n";
        $xml .= '          <xProd>' . htmlspecialchars($prod['descricao']) . '</xProd>' . "\n";
        $xml .= '          <NCM>' . $prod['ncm'] . '</NCM>' . "\n";
        if (isset($prod['beneficio'])) {
            $xml .= '          <cBenef>' . $prod['beneficio'] . '</cBenef>' . "\n";
        }
        $xml .= '          <CFOP>' . $prod['cfop'] . '</CFOP>' . "\n";
        $xml .= '          <uCom>' . $prod['unidade_comercial'] . '</uCom>' . "\n";
        $xml .= '          <qCom>' . $prod['quantidade_comercial'] . '</qCom>' . "\n";
        $xml .= '          <vUnCom>' . $prod['valor_unitario_comercial'] . '</vUnCom>' . "\n";
        $xml .= '          <vProd>' . $prod['valor_produto'] . '</vProd>' . "\n";
        $xml .= '          <cEANTrib>' . $prod['ean_tributavel'] . '</cEANTrib>' . "\n";
        $xml .= '          <uTrib>' . $prod['unidade_tributavel'] . '</uTrib>' . "\n";
        $xml .= '          <qTrib>' . $prod['quantidade_tributavel'] . '</qTrib>' . "\n";
        $xml .= '          <vUnTrib>' . $prod['valor_unitario_tributavel'] . '</vUnTrib>' . "\n";
        if (isset($prod['valor_frete'])) {
            $xml .= '          <vFrete>' . $prod['valor_frete'] . '</vFrete>' . "\n";
        }
        if (isset($prod['valor_seguro'])) {
            $xml .= '          <vSeg>' . $prod['valor_seguro'] . '</vSeg>' . "\n";
        }
        if (isset($prod['valor_outros'])) {
            $xml .= '          <vOutro>' . $prod['valor_outros'] . '</vOutro>' . "\n";
        }
        $xml .= '          <indTot>' . $prod['indicador_total'] . '</indTot>' . "\n";
        
        // DI (Declaração de Importação)
        if (isset($prod['declaracao_importacao'])) {
            $xml .= $this->buildDIXml($prod['declaracao_importacao']);
        }
        
        if (isset($prod['pedido'])) {
            $xml .= '          <xPed>' . htmlspecialchars($prod['pedido']) . '</xPed>' . "\n";
        }
        if (isset($prod['item_pedido'])) {
            $xml .= '          <nItemPed>' . $prod['item_pedido'] . '</nItemPed>' . "\n";
        }
        
        // Informações adicionais do produto
        if (isset($prod['informacoes_adicionais'])) {
            $xml .= '          <infAdProd>' . htmlspecialchars($prod['informacoes_adicionais']) . '</infAdProd>' . "\n";
        }
        
        $xml .= '        </prod>' . "\n";
        return $xml;
    }

    /**
     * Constrói XML da DI (Declaração de Importação)
     */
    private function buildDIXml($di) {
        $xml = '          <DI>' . "\n";
        $xml .= '            <nDI>' . $di['numero_di'] . '</nDI>' . "\n";
        $xml .= '            <dDI>' . $di['data_di'] . '</dDI>' . "\n";
        $xml .= '            <xLocDesemb>' . htmlspecialchars($di['local_desembaraco']) . '</xLocDesemb>' . "\n";
        $xml .= '            <UFDesemb>' . $di['uf_desembaraco'] . '</UFDesemb>' . "\n";
        $xml .= '            <dDesemb>' . $di['data_desembaraco'] . '</dDesemb>' . "\n";
        $xml .= '            <tpViaTransp>' . $di['tipo_via_transporte'] . '</tpViaTransp>' . "\n";
        $xml .= '            <vAFRMM>' . $di['valor_afrmm'] . '</vAFRMM>' . "\n";
        $xml .= '            <tpIntermedio>' . $di['tipo_intermediacao'] . '</tpIntermedio>' . "\n";
        $xml .= '            <cExportador>' . $di['codigo_exportador'] . '</cExportador>' . "\n";
        
        // Adição
        if (isset($di['adicao'])) {
            $adicao = $di['adicao'];
            $xml .= '            <adi>' . "\n";
            $xml .= '              <nAdicao>' . $adicao['numero_adicao'] . '</nAdicao>' . "\n";
            $xml .= '              <nSeqAdic>' . $adicao['sequencia_adicao'] . '</nSeqAdic>' . "\n";
            $xml .= '              <cFabricante>' . $adicao['codigo_fabricante'] . '</cFabricante>' . "\n";
            $xml .= '            </adi>' . "\n";
        }
        
        $xml .= '          </DI>' . "\n";
        return $xml;
    }

    /**
     * Constrói XML dos impostos
     */
    private function buildImpostosXml($impostos) {
        $xml = '        <imposto>' . "\n";
        
        // ICMS
        if (isset($impostos['icms'])) {
            $icms = $impostos['icms'];
            $xml .= '          <ICMS>' . "\n";
            $xml .= '            <ICMS51>' . "\n";
            if (isset($icms['origem'])) {
                $xml .= '              <orig>' . $icms['origem'] . '</orig>' . "\n";
            }
            if (isset($icms['cst'])) {
                $xml .= '              <CST>' . $icms['cst'] . '</CST>' . "\n";
            }
            $xml .= '            </ICMS51>' . "\n";
            $xml .= '          </ICMS>' . "\n";
        }
        
        // IPI
        if (isset($impostos['ipi'])) {
            $ipi = $impostos['ipi'];
            $xml .= '          <IPI>' . "\n";
            if (isset($ipi['codigo_enquadramento'])) {
                $xml .= '            <cEnq>' . $ipi['codigo_enquadramento'] . '</cEnq>' . "\n";
            }
            if (isset($ipi['tributacao'])) {
                $trib = $ipi['tributacao'];
                $xml .= '            <IPITrib>' . "\n";
                $xml .= '              <CST>' . $trib['cst'] . '</CST>' . "\n";
                $xml .= '              <vBC>' . $trib['base_calculo'] . '</vBC>' . "\n";
                $xml .= '              <pIPI>' . $trib['aliquota'] . '</pIPI>' . "\n";
                $xml .= '              <vIPI>' . $trib['valor'] . '</vIPI>' . "\n";
                $xml .= '            </IPITrib>' . "\n";
            }
            $xml .= '          </IPI>' . "\n";
        }
        
        // II
        if (isset($impostos['ii'])) {
            $ii = $impostos['ii'];
            $xml .= '          <II>' . "\n";
            $xml .= '            <vBC>' . $ii['base_calculo'] . '</vBC>' . "\n";
            $xml .= '            <vDespAdu>' . $ii['despesas_aduaneiras'] . '</vDespAdu>' . "\n";
            $xml .= '            <vII>' . $ii['valor'] . '</vII>' . "\n";
            $xml .= '            <vIOF>' . $ii['valor_iof'] . '</vIOF>' . "\n";
            $xml .= '          </II>' . "\n";
        }
        
        // PIS
        if (isset($impostos['pis'])) {
            $pis = $impostos['pis'];
            $xml .= '          <PIS>' . "\n";
            $xml .= '            <PISAliq>' . "\n";
            $xml .= '              <CST>' . $pis['cst'] . '</CST>' . "\n";
            $xml .= '              <vBC>' . $pis['base_calculo'] . '</vBC>' . "\n";
            $xml .= '              <pPIS>' . $pis['aliquota'] . '</pPIS>' . "\n";
            $xml .= '              <vPIS>' . $pis['valor'] . '</vPIS>' . "\n";
            $xml .= '            </PISAliq>' . "\n";
            $xml .= '          </PIS>' . "\n";
        }
        
        // COFINS
        if (isset($impostos['cofins'])) {
            $cofins = $impostos['cofins'];
            $xml .= '          <COFINS>' . "\n";
            $xml .= '            <COFINSAliq>' . "\n";
            $xml .= '              <CST>' . $cofins['cst'] . '</CST>' . "\n";
            $xml .= '              <vBC>' . $cofins['base_calculo'] . '</vBC>' . "\n";
            $xml .= '              <pCOFINS>' . $cofins['aliquota'] . '</pCOFINS>' . "\n";
            $xml .= '              <vCOFINS>' . $cofins['valor'] . '</vCOFINS>' . "\n";
            $xml .= '            </COFINSAliq>' . "\n";
            $xml .= '          </COFINS>' . "\n";
        }
        
        $xml .= '        </imposto>' . "\n";
        return $xml;
    }

    /**
     * Constrói XML dos totais
     */
    private function buildTotalXml($total) {
        $xml = '      <total>' . "\n";
        
        if (isset($total['icms'])) {
            $icms = $total['icms'];
            $xml .= '        <ICMSTot>' . "\n";
            $xml .= '          <vBC>' . $icms['base_calculo'] . '</vBC>' . "\n";
            $xml .= '          <vICMS>' . $icms['valor_icms'] . '</vICMS>' . "\n";
            $xml .= '          <vICMSDeson>0.00</vICMSDeson>' . "\n";
            $xml .= '          <vFCPUFDest>0.00</vFCPUFDest>' . "\n";
            $xml .= '          <vICMSUFDest>0.00</vICMSUFDest>' . "\n";
            $xml .= '          <vICMSUFRemet>0.00</vICMSUFRemet>' . "\n";
            $xml .= '          <vFCP>0.00</vFCP>' . "\n";
            $xml .= '          <vBCST>0.00</vBCST>' . "\n";
            $xml .= '          <vST>0.00</vST>' . "\n";
            $xml .= '          <vFCPST>0.00</vFCPST>' . "\n";
            $xml .= '          <vFCPSTRet>0.00</vFCPSTRet>' . "\n";
            $xml .= '          <qBCMono>0.00</qBCMono>' . "\n";
            $xml .= '          <vICMSMono>0.00</vICMSMono>' . "\n";
            $xml .= '          <qBCMonoReten>0.00</qBCMonoReten>' . "\n";
            $xml .= '          <vICMSMonoReten>0.00</vICMSMonoReten>' . "\n";
            $xml .= '          <qBCMonoRet>0.00</qBCMonoRet>' . "\n";
            $xml .= '          <vICMSMonoRet>0.00</vICMSMonoRet>' . "\n";
            $xml .= '          <vProd>' . $icms['valor_produtos'] . '</vProd>' . "\n";
            $xml .= '          <vFrete>' . $icms['valor_frete'] . '</vFrete>' . "\n";
            $xml .= '          <vSeg>' . $icms['valor_seguro'] . '</vSeg>' . "\n";
            $xml .= '          <vDesc>' . $icms['valor_desconto'] . '</vDesc>' . "\n";
            $xml .= '          <vII>' . $icms['valor_ii'] . '</vII>' . "\n";
            $xml .= '          <vIPI>' . $icms['valor_ipi'] . '</vIPI>' . "\n";
            $xml .= '          <vIPIDevol>0.00</vIPIDevol>' . "\n";
            $xml .= '          <vPIS>' . $icms['valor_pis'] . '</vPIS>' . "\n";
            $xml .= '          <vCOFINS>' . $icms['valor_cofins'] . '</vCOFINS>' . "\n";
            $xml .= '          <vOutro>' . $icms['valor_outros'] . '</vOutro>' . "\n";
            $xml .= '          <vNF>' . $icms['valor_total_nf'] . '</vNF>' . "\n";
            $xml .= '          <vTotTrib>' . $icms['valor_total_tributos'] . '</vTotTrib>' . "\n";
            $xml .= '        </ICMSTot>' . "\n";
        }
        
        $xml .= '      </total>' . "\n";
        return $xml;
    }

    /**
     * Constrói XML do transporte
     */
    private function buildTranspXml($transp) {
        $xml = '      <transp>' . "\n";
        $xml .= '        <modFrete>' . $transp['modalidade_frete'] . '</modFrete>' . "\n";
        
        // Transportadora
        if (isset($transp['transportadora'])) {
            $transporta = $transp['transportadora'];
            $xml .= '        <transporta>' . "\n";
            $xml .= '          <CNPJ>' . $transporta['cnpj'] . '</CNPJ>' . "\n";
            $xml .= '          <xNome>' . htmlspecialchars($transporta['nome']) . '</xNome>' . "\n";
            $xml .= '          <xEnder>' . htmlspecialchars($transporta['endereco']) . '</xEnder>' . "\n";
            $xml .= '          <xMun>' . htmlspecialchars($transporta['municipio']) . '</xMun>' . "\n";
            $xml .= '          <UF>' . $transporta['uf'] . '</UF>' . "\n";
            $xml .= '        </transporta>' . "\n";
        }
        
        // Volumes
        if (isset($transp['volumes'])) {
            $vol = $transp['volumes'];
            $xml .= '        <vol>' . "\n";
            $xml .= '          <qVol>' . $vol['quantidade'] . '</qVol>' . "\n";
            $xml .= '          <esp>' . htmlspecialchars($vol['especie']) . '</esp>' . "\n";
            $xml .= '          <pesoL>' . $vol['peso_liquido'] . '</pesoL>' . "\n";
            $xml .= '          <pesoB>' . $vol['peso_bruto'] . '</pesoB>' . "\n";
            $xml .= '        </vol>' . "\n";
        }
        
        $xml .= '      </transp>' . "\n";
        return $xml;
    }

    /**
     * Constrói XML do pagamento
     */
    private function buildPagXml($pag) {
        $xml = '      <pag>' . "\n";
        $xml .= '        <detPag>' . "\n";
        $xml .= '          <tPag>' . $pag['forma'] . '</tPag>' . "\n";
        $xml .= '          <vPag>' . $pag['valor'] . '</vPag>' . "\n";
        $xml .= '        </detPag>' . "\n";
        $xml .= '      </pag>' . "\n";
        return $xml;
    }

    /**
     * Constrói XML das informações adicionais
     */
    private function buildInfAdicXml($infAdic) {
        $xml = '      <infAdic>' . "\n";
        $xml .= '        <infCpl>' . htmlspecialchars($infAdic['informacoes_complementares']) . '</infCpl>' . "\n";
        $xml .= '      </infAdic>' . "\n";
        return $xml;
    }

    /**
     * Constrói XML do responsável técnico
     */
    private function buildInfRespTecXml($infRespTec) {
        $xml = '      <infRespTec>' . "\n";
        $xml .= '        <CNPJ>' . $infRespTec['cnpj'] . '</CNPJ>' . "\n";
        $xml .= '        <xContato>' . htmlspecialchars($infRespTec['contato']) . '</xContato>' . "\n";
        $xml .= '        <email>' . $infRespTec['email'] . '</email>' . "\n";
        $xml .= '        <fone>' . $infRespTec['telefone'] . '</fone>' . "\n";
        $xml .= '      </infRespTec>' . "\n";
        return $xml;
    }

    /**
     * Constrói XML da assinatura digital
     */
    private function buildSignatureXml($signature) {
        $xml = '    <Signature xmlns="http://www.w3.org/2000/09/xmldsig#">' . "\n";
        $xml .= '      <SignedInfo>' . "\n";
        $xml .= '        <CanonicalizationMethod Algorithm="' . $signature['metodo_canonicalizacao'] . '"/>' . "\n";
        $xml .= '        <SignatureMethod Algorithm="' . $signature['metodo_assinatura'] . '"/>' . "\n";
        $xml .= '        <Reference URI="' . $signature['uri'] . '">' . "\n";
        $xml .= '          <Transforms>' . "\n";
        $xml .= '            <Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature"/>' . "\n";
        $xml .= '            <Transform Algorithm="http://www.w3.org/TR/2001/REC-xml-c14n-20010315"/>' . "\n";
        $xml .= '          </Transforms>' . "\n";
        $xml .= '          <DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha1"/>' . "\n";
        $xml .= '          <DigestValue>' . $signature['digest_value'] . '</DigestValue>' . "\n";
        $xml .= '        </Reference>' . "\n";
        $xml .= '      </SignedInfo>' . "\n";
        $xml .= '      <SignatureValue>' . $signature['valor_assinatura'] . '</SignatureValue>' . "\n";
        $xml .= '      <KeyInfo>' . "\n";
        $xml .= '        <X509Data>' . "\n";
        $xml .= '          <X509Certificate>' . $signature['certificado_x509'] . '</X509Certificate>' . "\n";
        $xml .= '        </X509Data>' . "\n";
        $xml .= '      </KeyInfo>' . "\n";
        $xml .= '    </Signature>' . "\n";
        return $xml;
    }

    /**
     * Constrói XML do protocolo
     */
    private function buildProtocoloXml($protocolo) {
        $xml = '  <protNFe versao="' . $protocolo['versao'] . '">' . "\n";
        $xml .= '    <infProt>' . "\n";
        $xml .= '      <tpAmb>' . $protocolo['ambiente'] . '</tpAmb>' . "\n";
        $xml .= '      <verAplic>' . $protocolo['versao_aplicacao'] . '</verAplic>' . "\n";
        $xml .= '      <chNFe>' . $protocolo['chave_nfe'] . '</chNFe>' . "\n";
        $xml .= '      <dhRecbto>' . $protocolo['data_recebimento'] . '</dhRecbto>' . "\n";
        $xml .= '      <nProt>' . $protocolo['numero_protocolo'] . '</nProt>' . "\n";
        $xml .= '      <digVal>' . $protocolo['digest_value'] . '</digVal>' . "\n";
        $xml .= '      <cStat>' . $protocolo['codigo_status'] . '</cStat>' . "\n";
        $xml .= '      <xMotivo>' . htmlspecialchars($protocolo['motivo']) . '</xMotivo>' . "\n";
        $xml .= '    </infProt>' . "\n";
        $xml .= '  </protNFe>' . "\n";
        return $xml;
    }

    /**
     * Exibe o formulário de upload
     */
    private function showForm() {
        try {
            // Tenta usar View do Kohana se disponível
            if (class_exists('View')) {
                $view = View::factory('json2xml/form');
                $this->response->body($view);
            } else {
                // Fallback: HTML direto
                $this->showFormHtml();
            }
        } catch (Exception $e) {
            // Se der erro com View, usa HTML direto
            $this->showFormHtml();
        }
    }

    /**
     * Exibe formulário HTML direto
     */
    private function showFormHtml() {
        header('Content-Type: text/html; charset=UTF-8');
        
        $html = '<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON para XML - Converter procNfe</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; background-color: #f5f5f5; }
        .container { background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; margin-bottom: 30px; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; color: #555; }
        input[type="file"], textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; }
        textarea { height: 200px; resize: vertical; font-family: monospace; }
        .btn { background-color: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; width: 100%; }
        .btn:hover { background-color: #0056b3; }
        .info { background-color: #e7f3ff; border: 1px solid #b3d7ff; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
        .divider { text-align: center; margin: 20px 0; position: relative; }
        .divider::before { content: ""; position: absolute; top: 50%; left: 0; right: 0; height: 1px; background-color: #ddd; }
        .divider span { background-color: white; padding: 0 15px; color: #666; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 JSON para XML - Converter procNfe</h1>
        
        <div class="info">
            <h3>📋 Como usar:</h3>
            <ul>
                <li>Faça upload de um arquivo JSON no formato procNfe</li>
                <li>Ou cole o conteúdo JSON diretamente no campo de texto</li>
                <li>O sistema irá converter para XML e baixar automaticamente</li>
            </ul>
        </div>

        <form method="POST" enctype="multipart/form-data">
            <div class="form-group">
                <label for="json_file">📁 Upload arquivo JSON:</label>
                <input type="file" id="json_file" name="json_file" accept=".json">
            </div>

            <div class="divider"><span>OU</span></div>

            <div class="form-group">
                <label for="json_content">📝 Cole o conteúdo JSON:</label>
                <textarea id="json_content" name="json_content" placeholder="Cole seu JSON aqui..."></textarea>
            </div>

            <button type="submit" class="btn">🚀 Converter JSON para XML</button>
        </form>
    </div>

    <script>
        document.getElementById("json_file").addEventListener("change", function() {
            if (this.files.length > 0) document.getElementById("json_content").value = "";
        });
        document.getElementById("json_content").addEventListener("input", function() {
            if (this.value.trim() !== "") document.getElementById("json_file").value = "";
        });
    </script>
</body>
</html>';
        
        echo $html;
    }

    /**
     * Exibe erro
     */
    private function showError($message) {
        try {
            // Tenta usar View do Kohana se disponível
            if (class_exists('View')) {
                $view = View::factory('json2xml/error');
                $view->error_message = $message;
                $this->response->body($view);
            } else {
                // Fallback: HTML direto
                $this->showErrorHtml($message);
            }
        } catch (Exception $e) {
            // Se der erro com View, usa HTML direto
            $this->showErrorHtml($message);
        }
    }

    /**
     * Exibe erro HTML direto
     */
    private function showErrorHtml($message) {
        header('Content-Type: text/html; charset=UTF-8');
        
        $html = '<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Erro - JSON para XML</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; background-color: #f5f5f5; }
        .container { background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 20px; border-radius: 4px; margin-bottom: 20px; }
        .error h2 { margin-top: 0; color: #721c24; }
        .btn { background-color: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; }
        .btn:hover { background-color: #545b62; }
    </style>
</head>
<body>
    <div class="container">
        <div class="error">
            <h2>❌ Erro na Conversão</h2>
            <p><strong>Detalhes do erro:</strong></p>
            <p>' . htmlspecialchars($message) . '</p>
        </div>
        
        <a href="javascript:history.back()" class="btn">⬅️ Voltar</a>
    </div>
</body>
</html>';
        
        echo $html;
    }
}
