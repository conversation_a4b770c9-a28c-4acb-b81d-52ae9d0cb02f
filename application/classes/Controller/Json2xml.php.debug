<?php defined('SYSPATH') or die('No direct script access.');

/**
 * Controller Json2xml - Versão Debug
 * Versão simplificada para debug de erros
 */
class Controller_Json2xml extends Controller_Websession {

    public function action_index() {
        // Debug extremo
        error_log("=== JSON2XML DEBUG START ===");
        error_log("REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD']);
        error_log("POST data: " . print_r($_POST, true));
        error_log("FILES data: " . print_r($_FILES, true));
        
        try {
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                error_log("Processing POST request");
                
                // Verifica se há conteúdo JSON
                $jsonContent = '';
                $filename = 'converted';
                
                if (isset($_FILES['json_file']) && $_FILES['json_file']['error'] === UPLOAD_ERR_OK) {
                    error_log("File upload detected");
                    $jsonContent = file_get_contents($_FILES['json_file']['tmp_name']);
                    $filename = $_FILES['json_file']['name'];
                } elseif (isset($_POST['json_content']) && !empty($_POST['json_content'])) {
                    error_log("JSON content detected");
                    $jsonContent = $_POST['json_content'];
                } else {
                    error_log("No JSON content found");
                    throw new Exception('Nenhum conteúdo JSON fornecido');
                }
                
                error_log("JSON size: " . strlen($jsonContent) . " bytes");
                
                // Testa decodificação básica
                $data = json_decode($jsonContent, true);
                if ($data === null) {
                    $error = 'JSON inválido: ' . json_last_error_msg();
                    error_log("JSON decode error: " . $error);
                    throw new Exception($error);
                }
                
                error_log("JSON decoded successfully");
                
                // Valida estrutura
                if (!isset($data['nfe']) || !isset($data['protocolo'])) {
                    $error = 'Estrutura JSON inválida';
                    error_log("Structure validation error: " . $error);
                    throw new Exception($error);
                }
                
                error_log("Structure validation passed");
                
                // Gera XML simples
                $xml = $this->generateSimpleXml($data);
                
                error_log("XML generated, size: " . strlen($xml) . " bytes");
                
                // Output direto
                $outputFilename = pathinfo($filename, PATHINFO_FILENAME) . '_converted.xml';
                
                // Headers
                header('Content-Type: application/xml; charset=UTF-8');
                header('Content-Disposition: attachment; filename="' . $outputFilename . '"');
                header('Content-Length: ' . strlen($xml));
                
                // Limpa buffer
                while (ob_get_level()) {
                    ob_end_clean();
                }
                
                echo $xml;
                
                error_log("=== JSON2XML DEBUG SUCCESS ===");
                exit;
                
            } else {
                error_log("Showing form");
                $this->showSimpleForm();
            }
            
        } catch (Exception $e) {
            error_log("=== JSON2XML DEBUG ERROR ===");
            error_log("Error message: " . $e->getMessage());
            error_log("Error trace: " . $e->getTraceAsString());
            
            $this->showSimpleError($e->getMessage());
        }
    }
    
    private function generateSimpleXml($data) {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<nfeProc versao="' . $data['versao'] . '" xmlns="http://www.portalfiscal.inf.br/nfe">' . "\n";
        
        // NFe básica
        $xml .= '  <NFe xmlns="http://www.portalfiscal.inf.br/nfe">' . "\n";
        $xml .= '    <infNFe Id="' . $data['nfe']['id'] . '" versao="' . $data['nfe']['versao'] . '">' . "\n";
        $xml .= '      <!-- Conversão JSON para XML -->' . "\n";
        $xml .= '      <!-- Itens: ' . count($data['nfe']['itens']) . ' -->' . "\n";
        
        // Dados básicos
        $xml .= '      <ide>' . "\n";
        $xml .= '        <mod>55</mod>' . "\n";
        $xml .= '        <serie>1</serie>' . "\n";
        $xml .= '        <dhEmi>2025-07-24T12:00:00-03:00</dhEmi>' . "\n";
        $xml .= '      </ide>' . "\n";
        
        $xml .= '    </infNFe>' . "\n";
        $xml .= '  </NFe>' . "\n";
        
        // Protocolo
        $xml .= '  <protNFe versao="' . $data['protocolo']['versao'] . '">' . "\n";
        $xml .= '    <infProt>' . "\n";
        $xml .= '      <chNFe>' . $data['protocolo']['chave_nfe'] . '</chNFe>' . "\n";
        $xml .= '      <nProt>' . $data['protocolo']['numero_protocolo'] . '</nProt>' . "\n";
        $xml .= '      <cStat>' . $data['protocolo']['codigo_status'] . '</cStat>' . "\n";
        $xml .= '      <xMotivo>' . htmlspecialchars($data['protocolo']['motivo']) . '</xMotivo>' . "\n";
        $xml .= '    </infProt>' . "\n";
        $xml .= '  </protNFe>' . "\n";
        
        $xml .= '</nfeProc>';
        
        return $xml;
    }
    
    private function showSimpleForm() {
        header('Content-Type: text/html; charset=UTF-8');
        echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>JSON para XML - Debug</title>
    <style>
        body { font-family: Arial; max-width: 800px; margin: 50px auto; padding: 20px; }
        .container { background: white; padding: 30px; border: 1px solid #ddd; }
        input, textarea { width: 100%; padding: 10px; margin: 10px 0; }
        textarea { height: 200px; font-family: monospace; }
        button { background: #007bff; color: white; padding: 15px 30px; border: none; cursor: pointer; }
        .debug { background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 JSON para XML - Modo Debug</h1>
        
        <div class="debug">
            <strong>Debug Mode:</strong> Esta versão mostra logs detalhados para identificar problemas.
        </div>
        
        <form method="POST" enctype="multipart/form-data">
            <label>📁 Upload JSON:</label>
            <input type="file" name="json_file" accept=".json">
            
            <label>📝 Ou cole JSON:</label>
            <textarea name="json_content" placeholder="Cole seu JSON aqui..."></textarea>
            
            <button type="submit">🚀 Converter (Debug)</button>
        </form>
    </div>
</body>
</html>';
    }
    
    private function showSimpleError($message) {
        header('Content-Type: text/html; charset=UTF-8');
        echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Erro - JSON para XML</title>
    <style>
        body { font-family: Arial; max-width: 600px; margin: 50px auto; padding: 20px; }
        .error { background: #f8d7da; color: #721c24; padding: 20px; border: 1px solid #f5c6cb; }
        .btn { background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; }
    </style>
</head>
<body>
    <div class="error">
        <h2>❌ Erro na Conversão</h2>
        <p><strong>Erro:</strong> ' . htmlspecialchars($message) . '</p>
        <p><strong>Dica:</strong> Verifique os logs do servidor para mais detalhes.</p>
    </div>
    <a href="javascript:history.back()" class="btn">⬅️ Voltar</a>
</body>
</html>';
    }
}
