<?php

/**
 * Parser para arquivos procNFe (NFe processada com protocolo)
 */
class ProcNfeParser {
    
    /**
     * Converte um arquivo XML procNfe para array PHP estruturado
     * 
     * @param string $xmlFile Caminho para o arquivo XML
     * @return array Array com estrutura completa do procNfe
     */
    public static function parseXmlToArray($xmlFile) {
        if (!file_exists($xmlFile)) {
            throw new Exception("Arquivo XML não encontrado: $xmlFile");
        }
        
        // Carrega o XML
        $xmlContent = file_get_contents($xmlFile);
        
        // Remove namespace para facilitar o processamento
        $xmlContent = preg_replace('/xmlns[^=]*="[^"]*"/i', '', $xmlContent);
        
        $xml = simplexml_load_string($xmlContent);
        
        if ($xml === false) {
            throw new Exception("Erro ao processar XML");
        }
        
        $result = array();
        
        // Processa a NFe contida no procNfe
        if (isset($xml->NFe)) {
            $nfe = $xml->NFe;
            $result['nfe'] = self::parseNfe($nfe);
        }
        
        // Processa o protocolo
        if (isset($xml->protNFe)) {
            $protNfe = $xml->protNFe;
            $result['protocolo'] = self::parseProtocolo($protNfe);
        }
        
        // Informações do procNfe
        $result['versao'] = (string) $xml['versao'];
        
        return $result;
    }
    
    /**
     * Processa a seção NFe
     */
    private static function parseNfe($nfe) {
        $nfeData = array();
        
        if (isset($nfe->infNFe)) {
            $infNFe = $nfe->infNFe;
            
            // Identificação
            if (isset($infNFe->ide)) {
                $nfeData['identificacao'] = self::parseIdentificacao($infNFe->ide);
            }
            
            // Emitente
            if (isset($infNFe->emit)) {
                $nfeData['emitente'] = self::parseEmitente($infNFe->emit);
            }
            
            // Destinatário
            if (isset($infNFe->dest)) {
                $nfeData['destinatario'] = self::parseDestinatario($infNFe->dest);
            }
            
            // Detalhes (itens) - processa múltiplos elementos det
            $nfeData['itens'] = array();
            $xpath = $infNFe->xpath('det');
            if ($xpath) {
                $nfeData['itens'] = self::parseItens($xpath);
            }
            
            // Total
            if (isset($infNFe->total)) {
                $nfeData['total'] = self::parseTotal($infNFe->total);
            }
            
            // Transporte
            if (isset($infNFe->transp)) {
                $nfeData['transporte'] = self::parseTransporte($infNFe->transp);
            }
            
            // Pagamento
            if (isset($infNFe->pag)) {
                $nfeData['pagamento'] = self::parsePagamento($infNFe->pag);
            }
            
            // Informações adicionais
            if (isset($infNFe->infAdic)) {
                $nfeData['informacoes_adicionais'] = self::parseInfAdic($infNFe->infAdic);
            }
            
            // Responsável técnico
            if (isset($infNFe->infRespTec)) {
                $nfeData['responsavel_tecnico'] = self::parseInfRespTec($infNFe->infRespTec);
            }
            
            // ID da NFe
            $nfeData['id'] = (string) $infNFe['Id'];
            $nfeData['versao'] = (string) $infNFe['versao'];
        }
        
        // Assinatura
        if (isset($nfe->Signature)) {
            $nfeData['assinatura'] = self::parseAssinatura($nfe->Signature);
        }
        
        return $nfeData;
    }
    
    /**
     * Processa a seção de protocolo
     */
    private static function parseProtocolo($protNfe) {
        $protocolo = array();
        
        $protocolo['versao'] = (string) $protNfe['versao'];
        
        if (isset($protNfe->infProt)) {
            $infProt = $protNfe->infProt;
            
            $protocolo['ambiente'] = (string) $infProt->tpAmb;
            $protocolo['versao_aplicacao'] = (string) $infProt->verAplic;
            $protocolo['chave_nfe'] = (string) $infProt->chNFe;
            $protocolo['data_recebimento'] = (string) $infProt->dhRecbto;
            $protocolo['numero_protocolo'] = (string) $infProt->nProt;
            $protocolo['digest_value'] = (string) $infProt->digVal;
            $protocolo['codigo_status'] = (string) $infProt->cStat;
            $protocolo['motivo'] = (string) $infProt->xMotivo;
        }
        
        return $protocolo;
    }
    
    /**
     * Processa a identificação da NFe
     */
    private static function parseIdentificacao($ide) {
        return array(
            'codigo_uf' => (string) $ide->cUF,
            'codigo_nf' => (string) $ide->cNF,
            'natureza_operacao' => (string) $ide->natOp,
            'modelo' => (string) $ide->mod,
            'serie' => (string) $ide->serie,
            'numero_nf' => (string) $ide->nNF,
            'data_emissao' => (string) $ide->dhEmi,
            'data_saida_entrada' => (string) $ide->dhSaiEnt,
            'tipo_nf' => (string) $ide->tpNF,
            'identificacao_destino' => (string) $ide->idDest,
            'codigo_municipio_fg' => (string) $ide->cMunFG,
            'tipo_impressao' => (string) $ide->tpImp,
            'tipo_emissao' => (string) $ide->tpEmis,
            'digito_verificador' => (string) $ide->cDV,
            'tipo_ambiente' => (string) $ide->tpAmb,
            'finalidade_nfe' => (string) $ide->finNFe,
            'indicador_consumidor_final' => (string) $ide->indFinal,
            'indicador_presenca' => (string) $ide->indPres,
            'processo_emissao' => (string) $ide->procEmi,
            'versao_processo' => (string) $ide->verProc,
        );
    }
    
    /**
     * Processa dados do emitente
     */
    private static function parseEmitente($emit) {
        $emitente = array();
        
        if (isset($emit->CNPJ)) {
            $emitente['cnpj'] = (string) $emit->CNPJ;
        }
        
        $emitente['nome'] = (string) $emit->xNome;
        $emitente['fantasia'] = (string) $emit->xFant;
        
        // Endereço
        if (isset($emit->enderEmit)) {
            $endereco = $emit->enderEmit;
            $emitente['endereco'] = array(
                'logradouro' => (string) $endereco->xLgr,
                'numero' => (string) $endereco->nro,
                'bairro' => (string) $endereco->xBairro,
                'codigo_municipio' => (string) $endereco->cMun,
                'municipio' => (string) $endereco->xMun,
                'uf' => (string) $endereco->UF,
                'cep' => (string) $endereco->CEP,
                'codigo_pais' => (string) $endereco->cPais,
                'pais' => (string) $endereco->xPais,
                'telefone' => (string) $endereco->fone,
            );
        }
        
        $emitente['inscricao_estadual'] = (string) $emit->IE;
        $emitente['inscricao_municipal'] = (string) $emit->IM;
        $emitente['cnae'] = (string) $emit->CNAE;
        $emitente['crt'] = (string) $emit->CRT;
        
        return $emitente;
    }
    
    /**
     * Processa dados do destinatário
     */
    private static function parseDestinatario($dest) {
        $destinatario = array();
        
        if (isset($dest->CNPJ)) {
            $destinatario['cnpj'] = (string) $dest->CNPJ;
        } elseif (isset($dest->CPF)) {
            $destinatario['cpf'] = (string) $dest->CPF;
        } elseif (isset($dest->idEstrangeiro)) {
            $destinatario['id_estrangeiro'] = (string) $dest->idEstrangeiro;
        }
        
        $destinatario['nome'] = (string) $dest->xNome;
        
        // Endereço
        if (isset($dest->enderDest)) {
            $endereco = $dest->enderDest;
            $destinatario['endereco'] = array(
                'logradouro' => (string) $endereco->xLgr,
                'numero' => (string) $endereco->nro,
                'complemento' => (string) $endereco->xCpl,
                'bairro' => (string) $endereco->xBairro,
                'codigo_municipio' => (string) $endereco->cMun,
                'municipio' => (string) $endereco->xMun,
                'uf' => (string) $endereco->UF,
                'codigo_pais' => (string) $endereco->cPais,
                'pais' => (string) $endereco->xPais,
            );
        }
        
        $destinatario['indicador_ie'] = (string) $dest->indIEDest;
        
        return $destinatario;
    }
    
    /**
     * Processa os itens da NFe
     */
    private static function parseItens($detalhes) {
        $itens = array();
        
        // $detalhes já vem como array do xpath
        foreach ($detalhes as $det) {
            $item = array();
            $item['numero_item'] = (string) $det['nItem'];
            
            // Produto
            if (isset($det->prod)) {
                $prod = $det->prod;
                $item['produto'] = array(
                    'codigo' => (string) $prod->cProd,
                    'ean' => (string) $prod->cEAN,
                    'descricao' => (string) $prod->xProd,
                    'ncm' => (string) $prod->NCM,
                    'beneficio' => (string) $prod->cBenef,
                    'cfop' => (string) $prod->CFOP,
                    'unidade_comercial' => (string) $prod->uCom,
                    'quantidade_comercial' => (string) $prod->qCom,
                    'valor_unitario_comercial' => (string) $prod->vUnCom,
                    'valor_produto' => (string) $prod->vProd,
                    'ean_tributavel' => (string) $prod->cEANTrib,
                    'unidade_tributavel' => (string) $prod->uTrib,
                    'quantidade_tributavel' => (string) $prod->qTrib,
                    'valor_unitario_tributavel' => (string) $prod->vUnTrib,
                    'valor_frete' => (string) $prod->vFrete,
                    'valor_seguro' => (string) $prod->vSeg,
                    'valor_outros' => (string) $prod->vOutro,
                    'indicador_total' => (string) $prod->indTot,
                    'pedido' => (string) $prod->xPed,
                    'item_pedido' => (string) $prod->nItemPed,
                );
                
                // DI (Declaração de Importação)
                if (isset($prod->DI)) {
                    $di = $prod->DI;
                    $item['produto']['declaracao_importacao'] = array(
                        'numero_di' => (string) $di->nDI,
                        'data_di' => (string) $di->dDI,
                        'local_desembaraco' => (string) $di->xLocDesemb,
                        'uf_desembaraco' => (string) $di->UFDesemb,
                        'data_desembaraco' => (string) $di->dDesemb,
                        'tipo_via_transporte' => (string) $di->tpViaTransp,
                        'valor_afrmm' => (string) $di->vAFRMM,
                        'tipo_intermediacao' => (string) $di->tpIntermedio,
                        'codigo_exportador' => (string) $di->cExportador,
                    );
                    
                    // Adições
                    if (isset($di->adi)) {
                        $adicao = $di->adi;
                        $item['produto']['declaracao_importacao']['adicao'] = array(
                            'numero_adicao' => (string) $adicao->nAdicao,
                            'sequencia_adicao' => (string) $adicao->nSeqAdic,
                            'codigo_fabricante' => (string) $adicao->cFabricante,
                        );
                    }
                }
                
                // Informações adicionais do produto
                if (isset($prod->infAdProd)) {
                    $item['produto']['informacoes_adicionais'] = (string) $prod->infAdProd;
                }
            }
            
            // Impostos
            if (isset($det->imposto)) {
                $imposto = $det->imposto;
                $item['impostos'] = array();
                
                // ICMS
                if (isset($imposto->ICMS)) {
                    $icms = $imposto->ICMS;
                    if (isset($icms->ICMS51)) {
                        $item['impostos']['icms'] = array(
                            'origem' => (string) $icms->ICMS51->orig,
                            'cst' => (string) $icms->ICMS51->CST,
                        );
                    }
                }
                
                // IPI
                if (isset($imposto->IPI)) {
                    $ipi = $imposto->IPI;
                    $item['impostos']['ipi'] = array(
                        'codigo_enquadramento' => (string) $ipi->cEnq,
                    );
                    
                    if (isset($ipi->IPITrib)) {
                        $item['impostos']['ipi']['tributacao'] = array(
                            'cst' => (string) $ipi->IPITrib->CST,
                            'base_calculo' => (string) $ipi->IPITrib->vBC,
                            'aliquota' => (string) $ipi->IPITrib->pIPI,
                            'valor' => (string) $ipi->IPITrib->vIPI,
                        );
                    }
                }
                
                // II (Imposto de Importação)
                if (isset($imposto->II)) {
                    $ii = $imposto->II;
                    $item['impostos']['ii'] = array(
                        'base_calculo' => (string) $ii->vBC,
                        'despesas_aduaneiras' => (string) $ii->vDespAdu,
                        'valor' => (string) $ii->vII,
                        'valor_iof' => (string) $ii->vIOF,
                    );
                }
                
                // PIS
                if (isset($imposto->PIS)) {
                    $pis = $imposto->PIS;
                    if (isset($pis->PISAliq)) {
                        $item['impostos']['pis'] = array(
                            'cst' => (string) $pis->PISAliq->CST,
                            'base_calculo' => (string) $pis->PISAliq->vBC,
                            'aliquota' => (string) $pis->PISAliq->pPIS,
                            'valor' => (string) $pis->PISAliq->vPIS,
                        );
                    }
                }
                
                // COFINS
                if (isset($imposto->COFINS)) {
                    $cofins = $imposto->COFINS;
                    if (isset($cofins->COFINSAliq)) {
                        $item['impostos']['cofins'] = array(
                            'cst' => (string) $cofins->COFINSAliq->CST,
                            'base_calculo' => (string) $cofins->COFINSAliq->vBC,
                            'aliquota' => (string) $cofins->COFINSAliq->pCOFINS,
                            'valor' => (string) $cofins->COFINSAliq->vCOFINS,
                        );
                    }
                }
            }
            
            $itens[] = $item;
        }
        
        return $itens;
    }
    
    /**
     * Processa os totais da NFe
     */
    private static function parseTotal($total) {
        $totais = array();
        
        if (isset($total->ICMSTot)) {
            $icmsTot = $total->ICMSTot;
            $totais['icms'] = array(
                'base_calculo' => (string) $icmsTot->vBC,
                'valor_icms' => (string) $icmsTot->vICMS,
                'valor_produtos' => (string) $icmsTot->vProd,
                'valor_frete' => (string) $icmsTot->vFrete,
                'valor_seguro' => (string) $icmsTot->vSeg,
                'valor_desconto' => (string) $icmsTot->vDesc,
                'valor_ii' => (string) $icmsTot->vII,
                'valor_ipi' => (string) $icmsTot->vIPI,
                'valor_pis' => (string) $icmsTot->vPIS,
                'valor_cofins' => (string) $icmsTot->vCOFINS,
                'valor_outros' => (string) $icmsTot->vOutro,
                'valor_total_nf' => (string) $icmsTot->vNF,
                'valor_total_tributos' => (string) $icmsTot->vTotTrib,
            );
        }
        
        return $totais;
    }
    
    /**
     * Processa dados de transporte
     */
    private static function parseTransporte($transp) {
        $transporte = array();
        
        $transporte['modalidade_frete'] = (string) $transp->modFrete;
        
        // Transportadora
        if (isset($transp->transporta)) {
            $transporta = $transp->transporta;
            $transporte['transportadora'] = array(
                'cnpj' => (string) $transporta->CNPJ,
                'nome' => (string) $transporta->xNome,
                'endereco' => (string) $transporta->xEnder,
                'municipio' => (string) $transporta->xMun,
                'uf' => (string) $transporta->UF,
            );
        }
        
        // Volume
        if (isset($transp->vol)) {
            $vol = $transp->vol;
            $transporte['volumes'] = array(
                'quantidade' => (string) $vol->qVol,
                'especie' => (string) $vol->esp,
                'peso_liquido' => (string) $vol->pesoL,
                'peso_bruto' => (string) $vol->pesoB,
            );
        }
        
        return $transporte;
    }
    
    /**
     * Processa dados de pagamento
     */
    private static function parsePagamento($pag) {
        $pagamento = array();
        
        if (isset($pag->detPag)) {
            $detPag = $pag->detPag;
            $pagamento['forma'] = (string) $detPag->tPag;
            $pagamento['valor'] = (string) $detPag->vPag;
        }
        
        return $pagamento;
    }
    
    /**
     * Processa informações adicionais
     */
    private static function parseInfAdic($infAdic) {
        return array(
            'informacoes_complementares' => (string) $infAdic->infCpl,
        );
    }
    
    /**
     * Processa responsável técnico
     */
    private static function parseInfRespTec($infRespTec) {
        return array(
            'cnpj' => (string) $infRespTec->CNPJ,
            'contato' => (string) $infRespTec->xContato,
            'email' => (string) $infRespTec->email,
            'telefone' => (string) $infRespTec->fone,
        );
    }
    
    /**
     * Processa assinatura digital
     */
    private static function parseAssinatura($signature) {
        $assinatura = array();
        
        if (isset($signature->SignedInfo)) {
            $signedInfo = $signature->SignedInfo;
            $assinatura['metodo_canonicalizacao'] = (string) $signedInfo->CanonicalizationMethod['Algorithm'];
            $assinatura['metodo_assinatura'] = (string) $signedInfo->SignatureMethod['Algorithm'];
            
            if (isset($signedInfo->Reference)) {
                $reference = $signedInfo->Reference;
                $assinatura['uri'] = (string) $reference['URI'];
                $assinatura['digest_value'] = (string) $reference->DigestValue;
            }
        }
        
        if (isset($signature->SignatureValue)) {
            $assinatura['valor_assinatura'] = trim((string) $signature->SignatureValue);
        }
        
        if (isset($signature->KeyInfo->X509Data->X509Certificate)) {
            $assinatura['certificado_x509'] = trim((string) $signature->KeyInfo->X509Data->X509Certificate);
        }
        
        return $assinatura;
    }
}
