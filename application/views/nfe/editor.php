<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editor XML NFe</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/xml/xml.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/fold/foldcode.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/fold/foldgutter.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/fold/xml-fold.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/addon/fold/foldgutter.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">Editor XML NFe</h1>
                    <p class="text-gray-600">Arquivo: <?php echo htmlspecialchars(basename($file_path)); ?></p>
                </div>
                <div class="flex space-x-3">
                    <a href="/metrics/nfe/download" 
                       class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                        📥 Download
                    </a>
                    <button onclick="validateXML()" 
                            class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors">
                        ✓ Validar
                    </button>
                </div>
            </div>
        </div>

        <!-- NFe Info Panel -->
        <?php if (!empty($nfe_info)): ?>
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">📋 Informações da NFe</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="bg-blue-50 p-3 rounded-lg">
                    <div class="text-sm text-blue-600 font-medium">Número</div>
                    <div class="text-lg font-bold text-blue-800"><?php echo htmlspecialchars($nfe_info['numero']); ?></div>
                </div>
                <div class="bg-green-50 p-3 rounded-lg">
                    <div class="text-sm text-green-600 font-medium">Série</div>
                    <div class="text-lg font-bold text-green-800"><?php echo htmlspecialchars($nfe_info['serie']); ?></div>
                </div>
                <div class="bg-purple-50 p-3 rounded-lg">
                    <div class="text-sm text-purple-600 font-medium">Data Emissão</div>
                    <div class="text-lg font-bold text-purple-800"><?php echo htmlspecialchars($nfe_info['data_emissao']); ?></div>
                </div>
                <div class="bg-yellow-50 p-3 rounded-lg">
                    <div class="text-sm text-yellow-600 font-medium">Total de Itens</div>
                    <div class="text-lg font-bold text-yellow-800"><?php echo htmlspecialchars($nfe_info['total_itens']); ?></div>
                </div>
            </div>
            
            <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="bg-gray-50 p-3 rounded-lg">
                    <div class="text-sm text-gray-600 font-medium">Emitente</div>
                    <div class="font-bold text-gray-800"><?php echo htmlspecialchars($nfe_info['emitente_nome']); ?></div>
                    <div class="text-sm text-gray-600"><?php echo htmlspecialchars($nfe_info['emitente_cnpj']); ?></div>
                </div>
                <div class="bg-gray-50 p-3 rounded-lg">
                    <div class="text-sm text-gray-600 font-medium">Destinatário</div>
                    <div class="font-bold text-gray-800"><?php echo htmlspecialchars($nfe_info['destinatario_nome']); ?></div>
                    <div class="text-sm text-gray-600"><?php echo htmlspecialchars($nfe_info['destinatario_cnpj']); ?></div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Produtos Panel -->
        <?php if (!empty($produtos)): ?>
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-800">📦 Produtos da NFe</h2>
                <div class="text-sm text-gray-600">
                    Total: <?php echo count($produtos); ?> itens
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full table-auto">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Código</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Descrição</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">NCM</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qtd</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valor Unit.</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Valor Total</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($produtos as $produto): ?>
                        <tr id="produto-<?php echo $produto['nItem']; ?>" class="hover:bg-gray-50">
                            <td class="px-4 py-2 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <?php echo htmlspecialchars($produto['nItem']); ?>
                                </span>
                            </td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                                <?php echo htmlspecialchars($produto['cProd']); ?>
                            </td>
                            <td class="px-4 py-2 text-sm text-gray-900 max-w-xs">
                                <div class="truncate" title="<?php echo htmlspecialchars($produto['xProd']); ?>">
                                    <?php echo htmlspecialchars(substr($produto['xProd'], 0, 50)) . (strlen($produto['xProd']) > 50 ? '...' : ''); ?>
                                </div>
                            </td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                                <?php echo htmlspecialchars($produto['NCM']); ?>
                            </td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                                <?php echo htmlspecialchars($produto['qCom']); ?> <?php echo htmlspecialchars($produto['uCom']); ?>
                            </td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                                R$ <?php echo number_format(floatval($produto['vUnCom']), 2, ',', '.'); ?>
                            </td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">
                                R$ <?php echo number_format(floatval($produto['vProd']), 2, ',', '.'); ?>
                            </td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm font-medium">
                                <button onclick="removeProduto(<?php echo $produto['nItem']; ?>)"
                                        class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-xs transition-colors"
                                        title="Remover produto">
                                    🗑️ Remover
                                </button>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Resumo dos valores -->
            <div class="mt-4 bg-gray-50 p-4 rounded-lg">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="text-center">
                        <div class="text-sm text-gray-600">Total de Itens</div>
                        <div class="text-lg font-bold text-gray-800"><?php echo count($produtos); ?></div>
                    </div>
                    <div class="text-center">
                        <div class="text-sm text-gray-600">Quantidade Total</div>
                        <div class="text-lg font-bold text-gray-800">
                            <?php
                            $qtdTotal = 0;
                            foreach ($produtos as $produto) {
                                $qtdTotal += floatval($produto['qCom']);
                            }
                            echo number_format($qtdTotal, 2, ',', '.');
                            ?>
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="text-sm text-gray-600">Valor Total</div>
                        <div class="text-lg font-bold text-green-600">
                            R$ <?php
                            $valorTotal = 0;
                            foreach ($produtos as $produto) {
                                $valorTotal += floatval($produto['vProd']);
                            }
                            echo number_format($valorTotal, 2, ',', '.');
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Messages -->
        <?php if ($error): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-6">
            <div class="flex items-center">
                <span class="text-xl mr-2">❌</span>
                <strong class="font-bold">Erro:</strong>
                <span class="ml-2"><?php echo htmlspecialchars($error); ?></span>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($success): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mb-6">
            <div class="flex items-center">
                <span class="text-xl mr-2">✅</span>
                <strong class="font-bold">Sucesso:</strong>
                <span class="ml-2"><?php echo htmlspecialchars($success); ?></span>
            </div>
        </div>
        <?php endif; ?>

        <!-- Editor Form -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <form method="POST" id="xmlForm">
                <div class="mb-4">
                    <label for="xml_content" class="block text-sm font-medium text-gray-700 mb-2">
                        📝 Conteúdo XML
                    </label>
                    <textarea 
                        id="xml_content" 
                        name="xml_content" 
                        class="w-full h-96 p-3 border border-gray-300 rounded-lg font-mono text-sm"
                        placeholder="Cole aqui o conteúdo XML..."><?php echo htmlspecialchars($xml_content); ?></textarea>
                </div>
                
                <div class="flex justify-between items-center">
                    <div class="text-sm text-gray-500">
                        💡 Use Ctrl+F para buscar, Ctrl+G para ir para linha
                    </div>
                    <div class="flex space-x-3">
                        <button type="button" onclick="formatXML()" 
                                class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg transition-colors">
                            🎨 Formatar
                        </button>
                        <button type="submit" 
                                class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors">
                            💾 Salvar XML
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Validation Results -->
        <div id="validationResults" class="mt-6 hidden">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">🔍 Resultado da Validação</h3>
                <div id="validationContent"></div>
            </div>
        </div>
    </div>

    <script>
        // Initialize CodeMirror
        var editor = CodeMirror.fromTextArea(document.getElementById('xml_content'), {
            mode: 'xml',
            theme: 'monokai',
            lineNumbers: true,
            foldGutter: true,
            gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"],
            autoCloseTags: true,
            matchBrackets: true,
            indentUnit: 2,
            tabSize: 2,
            lineWrapping: true
        });

        // Format XML function
        function formatXML() {
            var content = editor.getValue();
            if (content.trim()) {
                try {
                    var parser = new DOMParser();
                    var xmlDoc = parser.parseFromString(content, "text/xml");
                    var serializer = new XMLSerializer();
                    var formatted = serializer.serializeToString(xmlDoc);
                    
                    // Simple formatting (basic indentation)
                    formatted = formatted.replace(/></g, '>\n<');
                    editor.setValue(formatted);
                } catch (e) {
                    alert('Erro ao formatar XML: ' + e.message);
                }
            }
        }

        // Validate XML function
        function validateXML() {
            var content = editor.getValue();
            
            fetch('/metrics/nfe/validate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'xml_content=' + encodeURIComponent(content)
            })
            .then(response => response.json())
            .then(data => {
                var resultsDiv = document.getElementById('validationResults');
                var contentDiv = document.getElementById('validationContent');
                
                if (data.valid) {
                    contentDiv.innerHTML = '<div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg"><div class="flex items-center"><span class="text-xl mr-2">✅</span><strong>XML Válido!</strong></div></div>';
                } else {
                    var errorsHtml = '<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg"><div class="flex items-center mb-2"><span class="text-xl mr-2">❌</span><strong>XML Inválido:</strong></div><ul class="list-disc list-inside">';
                    data.errors.forEach(function(error) {
                        errorsHtml += '<li>Linha ' + (error.line || '?') + ': ' + error.message + '</li>';
                    });
                    errorsHtml += '</ul></div>';
                    contentDiv.innerHTML = errorsHtml;
                }
                
                resultsDiv.classList.remove('hidden');
            })
            .catch(error => {
                console.error('Erro:', error);
                alert('Erro ao validar XML');
            });
        }

        // Remove produto function
        function removeProduto(nItem) {
            if (confirm('Tem certeza que deseja remover o produto ' + nItem + '?')) {
                fetch('/metrics/nfe/remove_produto', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'nItem=' + encodeURIComponent(nItem)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Remove a linha da tabela
                        var row = document.getElementById('produto-' + nItem);
                        if (row) {
                            row.style.transition = 'opacity 0.3s';
                            row.style.opacity = '0';
                            setTimeout(function() {
                                row.remove();
                                // Recarregar a página para atualizar o XML e reordenar os itens
                                location.reload();
                            }, 300);
                        }

                        // Mostrar mensagem de sucesso
                        showMessage(data.message, 'success');
                    } else {
                        showMessage(data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    showMessage('Erro ao remover produto', 'error');
                });
            }
        }

        // Show message function
        function showMessage(message, type) {
            var messageDiv = document.createElement('div');
            messageDiv.className = 'fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 transition-all duration-300';

            if (type === 'success') {
                messageDiv.className += ' bg-green-100 border border-green-400 text-green-700';
                messageDiv.innerHTML = '<div class="flex items-center"><span class="text-xl mr-2">✅</span>' + message + '</div>';
            } else {
                messageDiv.className += ' bg-red-100 border border-red-400 text-red-700';
                messageDiv.innerHTML = '<div class="flex items-center"><span class="text-xl mr-2">❌</span>' + message + '</div>';
            }

            document.body.appendChild(messageDiv);

            // Auto-remove after 3 seconds
            setTimeout(function() {
                messageDiv.style.opacity = '0';
                setTimeout(function() {
                    if (messageDiv.parentNode) {
                        messageDiv.parentNode.removeChild(messageDiv);
                    }
                }, 300);
            }, 3000);
        }

        // Auto-save functionality (optional)
        var saveTimeout;
        editor.on('change', function() {
            clearTimeout(saveTimeout);
            saveTimeout = setTimeout(function() {
                // Auto-save could be implemented here
            }, 5000);
        });
    </script>
</body>
</html>
