<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON para XML - Converter procNfe</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="file"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        textarea {
            height: 200px;
            resize: vertical;
            font-family: monospace;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .info {
            background-color: #e7f3ff;
            border: 1px solid #b3d7ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .info h3 {
            margin-top: 0;
            color: #0066cc;
        }
        .divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
        }
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background-color: #ddd;
        }
        .divider span {
            background-color: white;
            padding: 0 15px;
            color: #666;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 JSON para XML - Converter procNfe</h1>
        
        <div class="info">
            <h3>📋 Como usar:</h3>
            <ul>
                <li>Faça upload de um arquivo JSON no formato procNfe</li>
                <li>Ou cole o conteúdo JSON diretamente no campo de texto</li>
                <li>O sistema irá converter para XML e baixar automaticamente</li>
                <li>O JSON deve conter as estruturas "nfe" e "protocolo"</li>
            </ul>
        </div>

        <form method="POST" enctype="multipart/form-data">
            <div class="form-group">
                <label for="json_file">📁 Upload arquivo JSON:</label>
                <input type="file" id="json_file" name="json_file" accept=".json">
            </div>

            <div class="divider">
                <span>OU</span>
            </div>

            <div class="form-group">
                <label for="json_content">📝 Cole o conteúdo JSON:</label>
                <textarea id="json_content" name="json_content" placeholder='Exemplo:
{
    "nfe": {
        "id": "NFe42250701103171000613550010000286771070856190",
        "versao": "4.00",
        "itens": [...],
        ...
    },
    "protocolo": {
        "versao": "4.00",
        "ambiente": "1",
        ...
    },
    "versao": "4.00"
}'></textarea>
            </div>

            <button type="submit" class="btn">🚀 Converter JSON para XML</button>
        </form>
    </div>

    <script>
        // Limpa o campo oposto quando um é preenchido
        document.getElementById('json_file').addEventListener('change', function() {
            if (this.files.length > 0) {
                document.getElementById('json_content').value = '';
            }
        });

        document.getElementById('json_content').addEventListener('input', function() {
            if (this.value.trim() !== '') {
                document.getElementById('json_file').value = '';
            }
        });
    </script>
</body>
</html>
