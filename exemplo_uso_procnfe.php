<?php

require_once 'application/classes/ProcNfeParser.php';

try {
    // Caminho para o arquivo procNfe XML
    $xmlFile = '/home/<USER>/environment/Office/Apps/inProduction/metrics/application/views/42250701103171000613550010000286771070856190-procNfe.xml';
    
    echo "Processando procNfe XML...\n\n";
    
    // Converte o XML para array PHP
    $procNfeArray = ProcNfeParser::parseXmlToArray($xmlFile);
    
    echo "=== INFORMAÇÕES DO PROCNFE ===\n";
    echo "Versão: " . $procNfeArray['versao'] . "\n\n";
    
    // === NFE ===
    echo "=== DADOS DA NFE ===\n";
    $nfe = $procNfeArray['nfe'];
    
    echo "ID: " . $nfe['id'] . "\n";
    echo "Versão NFe: " . $nfe['versao'] . "\n\n";
    
    // Identificação
    echo "-- IDENTIFICAÇÃO --\n";
    $ident = $nfe['identificacao'];
    echo "Número NF: " . $ident['numero_nf'] . "\n";
    echo "Série: " . $ident['serie'] . "\n";
    echo "Natureza Operação: " . $ident['natureza_operacao'] . "\n";
    echo "Data Emissão: " . $ident['data_emissao'] . "\n\n";
    
    // Emitente
    echo "-- EMITENTE --\n";
    $emit = $nfe['emitente'];
    echo "CNPJ: " . $emit['cnpj'] . "\n";
    echo "Nome: " . $emit['nome'] . "\n";
    echo "Fantasia: " . $emit['fantasia'] . "\n";
    echo "Município: " . $emit['endereco']['municipio'] . " - " . $emit['endereco']['uf'] . "\n\n";
    
    // Destinatário
    echo "-- DESTINATÁRIO --\n";
    $dest = $nfe['destinatario'];
    echo "Nome: " . $dest['nome'] . "\n";
    echo "País: " . $dest['endereco']['pais'] . "\n";
    echo "Município: " . $dest['endereco']['municipio'] . "\n\n";
    
    // Resumo dos itens
    echo "-- RESUMO DOS ITENS --\n";
    $totalItens = count($nfe['itens']);
    echo "Total de itens: " . $totalItens . "\n";
    
    // Mostra os primeiros 3 itens como exemplo
    for ($i = 0; $i < min(3, $totalItens); $i++) {
        $item = $nfe['itens'][$i];
        echo "\nItem " . $item['numero_item'] . ":\n";
        echo "  Código: " . $item['produto']['codigo'] . "\n";
        echo "  Descrição: " . substr($item['produto']['descricao'], 0, 50) . "...\n";
        echo "  Quantidade: " . $item['produto']['quantidade_comercial'] . " " . $item['produto']['unidade_comercial'] . "\n";
        echo "  Valor Unitário: R$ " . number_format((float)$item['produto']['valor_unitario_comercial'], 2, ',', '.') . "\n";
        echo "  Valor Total: R$ " . number_format((float)$item['produto']['valor_produto'], 2, ',', '.') . "\n";
        
        // Informações de importação se existir
        if (isset($item['produto']['declaracao_importacao'])) {
            $di = $item['produto']['declaracao_importacao'];
            echo "  DI: " . $di['numero_di'] . " (Data: " . $di['data_di'] . ")\n";
        }
    }
    
    if ($totalItens > 3) {
        echo "\n... e mais " . ($totalItens - 3) . " itens.\n";
    }
    
    // Totais
    echo "\n-- TOTAIS --\n";
    $total = $nfe['total']['icms'];
    echo "Valor dos Produtos: R$ " . number_format((float)$total['valor_produtos'], 2, ',', '.') . "\n";
    echo "Valor do Frete: R$ " . number_format((float)$total['valor_frete'], 2, ',', '.') . "\n";
    echo "Valor do Seguro: R$ " . number_format((float)$total['valor_seguro'], 2, ',', '.') . "\n";
    echo "Valor de Outros: R$ " . number_format((float)$total['valor_outros'], 2, ',', '.') . "\n";
    echo "Valor II: R$ " . number_format((float)$total['valor_ii'], 2, ',', '.') . "\n";
    echo "Valor PIS: R$ " . number_format((float)$total['valor_pis'], 2, ',', '.') . "\n";
    echo "Valor COFINS: R$ " . number_format((float)$total['valor_cofins'], 2, ',', '.') . "\n";
    echo "VALOR TOTAL DA NF: R$ " . number_format((float)$total['valor_total_nf'], 2, ',', '.') . "\n\n";
    
    // Transporte
    echo "-- TRANSPORTE --\n";
    $transp = $nfe['transporte'];
    echo "Modalidade Frete: " . $transp['modalidade_frete'] . "\n";
    if (isset($transp['transportadora'])) {
        echo "Transportadora: " . $transp['transportadora']['nome'] . "\n";
        echo "CNPJ Transportadora: " . $transp['transportadora']['cnpj'] . "\n";
    }
    if (isset($transp['volumes'])) {
        echo "Quantidade de Volumes: " . $transp['volumes']['quantidade'] . "\n";
        echo "Peso Bruto: " . number_format((float)$transp['volumes']['peso_bruto'], 3, ',', '.') . " kg\n";
    }
    echo "\n";
    
    // === PROTOCOLO ===
    echo "=== PROTOCOLO DE AUTORIZAÇÃO ===\n";
    $protocolo = $procNfeArray['protocolo'];
    echo "Número do Protocolo: " . $protocolo['numero_protocolo'] . "\n";
    echo "Data Recebimento: " . $protocolo['data_recebimento'] . "\n";
    echo "Status: " . $protocolo['codigo_status'] . " - " . $protocolo['motivo'] . "\n";
    echo "Versão Aplicação: " . $protocolo['versao_aplicacao'] . "\n\n";
    
    // Salva o array completo em formato JSON para visualização
    $jsonFile = '/home/<USER>/environment/Office/Apps/inProduction/metrics/procnfe_array.json';
    file_put_contents($jsonFile, json_encode($procNfeArray, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    echo "Array completo salvo em: $jsonFile\n\n";
    
    echo "=== PROCESSAMENTO CONCLUÍDO ===\n";
    echo "O procNfe foi convertido com sucesso para array PHP!\n";
    echo "Estrutura contém:\n";
    echo "- NFe completa com " . count($nfe['itens']) . " itens de máquinas de costura\n";
    echo "- Protocolo de autorização da Receita Federal\n";
    echo "- Todas as informações fiscais e tributárias\n";
    echo "- Dados de importação (DI) para cada item\n";
    
} catch (Exception $e) {
    echo "Erro: " . $e->getMessage() . "\n";
}
