<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Kohana JSON2XML POST</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        textarea {
            width: 100%;
            height: 200px;
            font-family: monospace;
            font-size: 12px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Kohana JSON2XML Controller</h1>
        
        <div class="warning">
            <strong>⚠️ Debug Mode:</strong> This tests the Kohana controller at /json2xml/ - check logs for detailed debugging.
        </div>

        <form action="/metrics/json2xml/" method="POST" enctype="multipart/form-data">
            <label><strong>📝 JSON Content:</strong></label>
            <textarea name="json_content" placeholder="Cole o JSON aqui...">{
    "versao": "4.00",
    "nfe": {
        "id": "NFe42250701103171000613550010000286771070856190",
        "versao": "4.00",
        "itens": [
            {
                "numero_item": "1",
                "codigo_produto": "12345",
                "descricao": "Produto Teste",
                "quantidade": "1.0000",
                "valor_unitario": "100.00"
            }
        ]
    },
    "protocolo": {
        "versao": "4.00",
        "chave_nfe": "42250701103171000613550010000286771070856190",
        "numero_protocolo": "142250000123456",
        "codigo_status": "100",
        "motivo": "Autorizado o uso da NF-e"
    }
}</textarea>
            
            <button type="submit">🚀 Test Kohana Controller</button>
        </form>
        
        <hr style="margin: 30px 0;">
        
        <div style="font-size: 14px; color: #666;">
            <strong>Instructions:</strong><br>
            1. Click "Test Kohana Controller" to submit the JSON<br>
            2. Check the Docker logs for detailed debugging information<br>
            3. Look for error messages in the logs if it fails<br>
            4. The XML should download if successful
        </div>
    </div>
</body>
</html>
