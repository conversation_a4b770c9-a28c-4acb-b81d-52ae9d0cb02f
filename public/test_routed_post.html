<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Routed JSON2XML POST</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        textarea {
            width: 100%;
            height: 300px;
            font-family: monospace;
            font-size: 12px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        input[type="file"] {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #28a745;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background: #218838;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Routed JSON2XML POST</h1>
        
        <div class="success">
            <strong>✅ Working Version:</strong> This tests the routed json2xml.php file that should work reliably.
        </div>

        <form action="/metrics/json2xml.php" method="POST" enctype="multipart/form-data">
            <label><strong>📁 Upload JSON File:</strong></label>
            <input type="file" name="json_file" accept=".json">
            
            <label><strong>📝 Or paste JSON content:</strong></label>
            <textarea name="json_content" placeholder="Cole o JSON aqui...">{
    "versao": "4.00",
    "nfe": {
        "id": "NFe42250701103171000613550010000286771070856190",
        "versao": "4.00",
        "identificacao": {
            "codigo_uf": "42",
            "codigo_nf": "07085619",
            "natureza_operacao": "Compra para comercializacao",
            "modelo": "55",
            "serie": "1",
            "numero_nf": "28677",
            "data_emissao": "2025-07-22T08:56:00-03:00",
            "data_saida_entrada": "2025-07-22T08:56:00-03:00",
            "tipo_nf": "0"
        },
        "emitente": {
            "cnpj": "01103171000613",
            "nome": "ROLEMAK COMERCIAL LTDA",
            "fantasia": "ROLEMAK - 0006-13"
        },
        "destinatario": {
            "nome": "LEAD POWER INDUSTRIES LIMITED"
        },
        "itens": [
            {
                "numero_item": "1",
                "codigo_produto": "12345",
                "descricao": "Produto Teste",
                "quantidade": "1.0000",
                "valor_unitario": "100.00"
            }
        ]
    },
    "protocolo": {
        "versao": "4.00",
        "ambiente": "1",
        "chave_nfe": "42250701103171000613550010000286771070856190",
        "data_recebimento": "2025-07-23T17:35:28-03:00",
        "numero_protocolo": "242250280214503",
        "codigo_status": "100",
        "motivo": "Autorizado o uso da NF-e"
    }
}</textarea>
            
            <button type="submit">🚀 Convert to XML</button>
        </form>
        
        <hr style="margin: 30px 0;">
        
        <div style="font-size: 14px; color: #666;">
            <strong>Instructions:</strong><br>
            1. Either upload a JSON file or use the pre-filled JSON content<br>
            2. Click "Convert to XML" to test the conversion<br>
            3. The XML file should download automatically if successful<br>
            4. Check the logs for debugging information
        </div>
    </div>
</body>
</html>
