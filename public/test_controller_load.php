<?php
/**
 * Test if the Json2xml controller can be loaded
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🧪 Controller Load Test</h1>";
echo "<pre>";

try {
    // Try to include the Kohana bootstrap
    echo "1. Testing Kohana bootstrap...\n";
    
    // Set up basic paths
    $application = '../application';
    $modules = '../modules';
    $system = '../system';
    
    // Define paths
    define('DOCROOT', realpath('../').DIRECTORY_SEPARATOR);
    define('APPPATH', realpath($application).DIRECTORY_SEPARATOR);
    define('MODPATH', realpath($modules).DIRECTORY_SEPARATOR);
    define('SYSPATH', realpath($system).DIRECTORY_SEPARATOR);
    define('EXT', '.php');
    
    echo "✅ Paths defined\n";
    echo "   APPPATH: " . APPPATH . "\n";
    echo "   SYSPATH: " . SYSPATH . "\n";
    
    // Check if controller file exists
    $controllerFile = APPPATH . 'classes/Controller/Json2xml.php';
    if (file_exists($controllerFile)) {
        echo "✅ Controller file exists: $controllerFile\n";
    } else {
        throw new Exception("Controller file not found: $controllerFile");
    }
    
    // Try to include Kohana core
    if (file_exists(SYSPATH . 'classes/Kohana/Core.php')) {
        echo "✅ Kohana core found\n";
    } else {
        echo "❌ Kohana core not found\n";
    }
    
    // Try to include the bootstrap
    if (file_exists(APPPATH . 'bootstrap.php')) {
        echo "✅ Bootstrap file found\n";
        
        // Include bootstrap
        include APPPATH . 'bootstrap.php';
        echo "✅ Bootstrap loaded successfully\n";
        
        // Try to load the controller class
        if (class_exists('Controller_Json2xml')) {
            echo "✅ Controller_Json2xml class loaded\n";
            
            // Try to create an instance
            $request = Request::factory('/json2xml/test');
            $response = Response::factory();
            $controller = new Controller_Json2xml($request, $response);
            
            echo "✅ Controller instance created successfully\n";
            echo "   Controller class: " . get_class($controller) . "\n";
            
            // Test if action exists
            if (method_exists($controller, 'action_test')) {
                echo "✅ action_test method exists\n";
            } else {
                echo "❌ action_test method not found\n";
            }
            
            if (method_exists($controller, 'action_index')) {
                echo "✅ action_index method exists\n";
            } else {
                echo "❌ action_index method not found\n";
            }
            
        } else {
            echo "❌ Controller_Json2xml class not found\n";
            echo "Available classes: " . implode(', ', get_declared_classes()) . "\n";
        }
        
    } else {
        echo "❌ Bootstrap file not found: " . APPPATH . 'bootstrap.php' . "\n";
    }
    
    echo "\n🎉 Controller load test completed successfully!\n";
    echo "The controller should be accessible via the web interface.\n";
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
    echo "📋 Trace:\n" . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    echo "❌ FATAL ERROR: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
}

echo "</pre>";
?>
