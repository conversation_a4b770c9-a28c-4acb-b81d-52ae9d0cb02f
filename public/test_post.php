<?php
/**
 * J<PERSON><PERSON> para XML - Teste Básico POST
 */

// Mostra erros para debug
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>Teste POST Debug</h1>";
echo "<p>Método: " . $_SERVER['REQUEST_METHOD'] . "</p>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>POST recebido!</h2>";
    echo "<pre>";
    echo "POST data:\n";
    print_r($_POST);
    echo "\nFILES data:\n";
    print_r($_FILES);
    echo "</pre>";
    
    if (isset($_POST['json_content']) && !empty($_POST['json_content'])) {
        echo "<h3>JSON Content encontrado:</h3>";
        echo "<pre>" . htmlspecialchars($_POST['json_content']) . "</pre>";
        
        $data = json_decode($_POST['json_content'], true);
        if ($data) {
            echo "<h3>JSON decodificado com sucesso!</h3>";
            echo "<pre>" . print_r($data, true) . "</pre>";
        } else {
            echo "<h3>Erro ao decodificar JSON:</h3>";
            echo "<p>" . json_last_error_msg() . "</p>";
        }
    }
    
    if (isset($_FILES['json_file']) && $_FILES['json_file']['error'] === UPLOAD_ERR_OK) {
        echo "<h3>Arquivo enviado:</h3>";
        $content = file_get_contents($_FILES['json_file']['tmp_name']);
        echo "<pre>" . htmlspecialchars($content) . "</pre>";
    }
    
} else {
    echo '
<form method="POST" enctype="multipart/form-data">
    <h2>Teste de POST</h2>
    
    <label>Upload JSON:</label><br>
    <input type="file" name="json_file" accept=".json"><br><br>
    
    <label>JSON Text:</label><br>
    <textarea name="json_content" rows="10" cols="50">{"teste": "valor"}</textarea><br><br>
    
    <button type="submit">Enviar POST</button>
</form>';
}
?>
