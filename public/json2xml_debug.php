<?php
/**
 * J<PERSON>N para XML - Versão Standalone para Debug
 * Funciona independente do framework Kohana
 */

// Debug extremo
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Log para arquivo - caminho corrigido para o servidor
function debug_log($message) {
    $log_dir = '/var/www/html/application/logs/2025/07/';
    if (!is_dir($log_dir)) {
        @mkdir($log_dir, 0755, true);
    }
    @file_put_contents($log_dir . '24.php', date('[Y-m-d H:i:s]') . " JSON2XML DEBUG: $message\n", FILE_APPEND | LOCK_EX);
}

debug_log("=== JSON2XML STANDALONE START ===");
debug_log("REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD']);
debug_log("POST data: " . print_r($_POST, true));
debug_log("FILES data: " . print_r($_FILES, true));

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        debug_log("Processing POST request");
        
        // Verifica se há conteúdo JSON
        $jsonContent = '';
        $filename = 'converted';
        
        if (isset($_FILES['json_file']) && $_FILES['json_file']['error'] === UPLOAD_ERR_OK) {
            debug_log("File upload detected");
            $jsonContent = file_get_contents($_FILES['json_file']['tmp_name']);
            $filename = $_FILES['json_file']['name'];
        } elseif (isset($_POST['json_content']) && !empty($_POST['json_content'])) {
            debug_log("JSON content detected");
            $jsonContent = $_POST['json_content'];
        } else {
            debug_log("No JSON content found");
            throw new Exception('Nenhum conteúdo JSON fornecido');
        }
        
        debug_log("JSON size: " . strlen($jsonContent) . " bytes");
        
        // Testa decodificação básica
        $data = json_decode($jsonContent, true);
        if ($data === null) {
            $error = 'JSON inválido: ' . json_last_error_msg();
            debug_log("JSON decode error: " . $error);
            throw new Exception($error);
        }
        
        debug_log("JSON decoded successfully");
        
        // Valida estrutura
        if (!isset($data['nfe']) || !isset($data['protocolo'])) {
            $error = 'Estrutura JSON inválida';
            debug_log("Structure validation error: " . $error);
            throw new Exception($error);
        }
        
        debug_log("Structure validation passed");
        
        // Gera XML simples
        $xml = generateSimpleXml($data);
        
        debug_log("XML generated, size: " . strlen($xml) . " bytes");
        
        // Output direto
        $outputFilename = pathinfo($filename, PATHINFO_FILENAME) . '_converted.xml';
        
        // Headers
        header('Content-Type: application/xml; charset=UTF-8');
        header('Content-Disposition: attachment; filename="' . $outputFilename . '"');
        header('Content-Length: ' . strlen($xml));
        
        // Limpa buffer
        while (ob_get_level()) {
            ob_end_clean();
        }
        
        echo $xml;
        
        debug_log("=== JSON2XML DEBUG SUCCESS ===");
        exit;
        
    } else {
        debug_log("Showing form");
        showSimpleForm();
    }
    
} catch (Exception $e) {
    debug_log("=== JSON2XML DEBUG ERROR ===");
    debug_log("Error message: " . $e->getMessage());
    debug_log("Error trace: " . $e->getTraceAsString());
    
    showSimpleError($e->getMessage());
}

function generateSimpleXml($data) {
    $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    $xml .= '<nfeProc versao="' . $data['versao'] . '" xmlns="http://www.portalfiscal.inf.br/nfe">' . "\n";
    
    // NFe básica
    $xml .= '  <NFe xmlns="http://www.portalfiscal.inf.br/nfe">' . "\n";
    $xml .= '    <infNFe Id="' . $data['nfe']['id'] . '" versao="' . $data['nfe']['versao'] . '">' . "\n";
    $xml .= '      <!-- Conversão JSON para XML -->' . "\n";
    $xml .= '      <!-- Itens: ' . count($data['nfe']['itens']) . ' -->' . "\n";
    
    // Dados básicos
    $xml .= '      <ide>' . "\n";
    $xml .= '        <mod>55</mod>' . "\n";
    $xml .= '        <serie>1</serie>' . "\n";
    $xml .= '        <dhEmi>2025-07-24T12:00:00-03:00</dhEmi>' . "\n";
    $xml .= '      </ide>' . "\n";
    
    $xml .= '    </infNFe>' . "\n";
    $xml .= '  </NFe>' . "\n";
    
    // Protocolo
    $xml .= '  <protNFe versao="' . $data['protocolo']['versao'] . '">' . "\n";
    $xml .= '    <infProt>' . "\n";
    $xml .= '      <chNFe>' . $data['protocolo']['chave_nfe'] . '</chNFe>' . "\n";
    $xml .= '      <nProt>' . $data['protocolo']['numero_protocolo'] . '</nProt>' . "\n";
    $xml .= '      <cStat>' . $data['protocolo']['codigo_status'] . '</cStat>' . "\n";
    $xml .= '      <xMotivo>' . htmlspecialchars($data['protocolo']['motivo']) . '</xMotivo>' . "\n";
    $xml .= '    </infProt>' . "\n";
    $xml .= '  </protNFe>' . "\n";
    
    $xml .= '</nfeProc>';
    
    return $xml;
}

function showSimpleForm() {
    header('Content-Type: text/html; charset=UTF-8');
    echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>JSON para XML - Standalone Debug</title>
    <style>
        body { font-family: Arial; max-width: 800px; margin: 50px auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border: 1px solid #ddd; border-radius: 8px; }
        input, textarea { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ccc; border-radius: 4px; }
        textarea { height: 200px; font-family: monospace; }
        button { background: #28a745; color: white; padding: 15px 30px; border: none; cursor: pointer; border-radius: 4px; }
        button:hover { background: #218838; }
        .debug { background: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; border-radius: 4px; margin: 20px 0; }
        .warning { background: #fff3cd; color: #856404; padding: 15px; border: 1px solid #ffeaa7; border-radius: 4px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 JSON para XML - Standalone Debug</h1>
        
        <div class="debug">
            <strong>✅ Standalone Mode:</strong> Esta versão funciona independente do framework Kohana para identificar problemas.
        </div>
        
        <div class="warning">
            <strong>⚠️ Aviso:</strong> Se esta versão funcionar, significa que o problema está no framework Kohana, não no seu código.
        </div>
        
        <form method="POST" enctype="multipart/form-data">
            <label><strong>📁 Upload JSON:</strong></label>
            <input type="file" name="json_file" accept=".json">
            
            <label><strong>📝 Ou cole JSON:</strong></label>
            <textarea name="json_content" placeholder="Cole seu JSON aqui..."></textarea>
            
            <button type="submit">🚀 Converter (Standalone)</button>
        </form>
        
        <hr style="margin: 30px 0;">
        
        <div style="font-size: 14px; color: #666;">
            <strong>Debug Info:</strong><br>
            Servidor: ' . $_SERVER['SERVER_NAME'] . '<br>
            PHP: ' . PHP_VERSION . '<br>
            Timestamp: ' . date('Y-m-d H:i:s') . '
        </div>
    </div>
</body>
</html>';
}

function showSimpleError($message) {
    header('Content-Type: text/html; charset=UTF-8');
    echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Erro - JSON para XML</title>
    <style>
        body { font-family: Arial; max-width: 600px; margin: 50px auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border: 1px solid #ddd; border-radius: 8px; }
        .error { background: #f8d7da; color: #721c24; padding: 20px; border: 1px solid #f5c6cb; border-radius: 4px; }
        .btn { background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="error">
            <h2>❌ Erro na Conversão</h2>
            <p><strong>Erro:</strong> ' . htmlspecialchars($message) . '</p>
            <p><strong>Dica:</strong> Verifique os logs do servidor para mais detalhes.</p>
        </div>
        <br>
        <a href="javascript:history.back()" class="btn">⬅️ Voltar</a>
    </div>
</body>
</html>';
}
?>
