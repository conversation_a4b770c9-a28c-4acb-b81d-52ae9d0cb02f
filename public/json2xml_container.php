<?php
/**
 * JSON para XML - Versão Docker Container
 * Funciona dentro de containers sem dependências do Kohana
 */

// Headers de segurança para container
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');

// Configuração para container
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Log simples para container
function log_debug($message) {
    // Log para stdout (Docker logs)
    error_log("[JSON2XML] " . $message);
}

log_debug("Request started - Method: " . $_SERVER['REQUEST_METHOD']);

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        log_debug("Processing POST request");
        
        // Buffer output para evitar conflitos
        ob_start();
        
        // Verifica se há conteúdo JSON
        $jsonContent = '';
        $filename = 'converted';
        
        if (isset($_FILES['json_file']) && $_FILES['json_file']['error'] === UPLOAD_ERR_OK) {
            log_debug("File upload detected");
            $jsonContent = file_get_contents($_FILES['json_file']['tmp_name']);
            $filename = pathinfo($_FILES['json_file']['name'], PATHINFO_FILENAME);
        } elseif (isset($_POST['json_content']) && !empty($_POST['json_content'])) {
            log_debug("JSON content detected");
            $jsonContent = trim($_POST['json_content']);
        } else {
            throw new Exception('Nenhum conteúdo JSON fornecido');
        }
        
        log_debug("JSON size: " . strlen($jsonContent) . " bytes");
        
        // Remove BOM se existir
        $jsonContent = preg_replace('/^[\x00-\x1F\x80-\xFF]*/', '', $jsonContent);
        
        // Testa decodificação básica
        $data = json_decode($jsonContent, true);
        if ($data === null) {
            $error = 'JSON inválido: ' . json_last_error_msg();
            log_debug("JSON decode error: " . $error);
            throw new Exception($error);
        }
        
        log_debug("JSON decoded successfully");
        
        // Valida estrutura mínima
        if (!isset($data['nfe']) || !isset($data['protocolo'])) {
            $error = 'Estrutura JSON inválida - requer seções "nfe" e "protocolo"';
            log_debug("Structure validation error: " . $error);
            throw new Exception($error);
        }
        
        log_debug("Structure validation passed");
        
        // Gera XML
        $xml = generateXmlFromJson($data);
        
        log_debug("XML generated, size: " . strlen($xml) . " bytes");
        
        // Limpa buffer anterior
        ob_clean();
        
        // Headers para download
        $outputFilename = $filename . '_converted.xml';
        header('Content-Type: application/xml; charset=UTF-8');
        header('Content-Disposition: attachment; filename="' . $outputFilename . '"');
        header('Content-Length: ' . strlen($xml));
        header('Cache-Control: no-cache, must-revalidate');
        header('Pragma: no-cache');
        
        echo $xml;
        
        log_debug("XML output sent successfully");
        exit;
        
    } else {
        // Mostra formulário
        showForm();
    }
    
} catch (Exception $e) {
    log_debug("Error: " . $e->getMessage());
    
    // Limpa qualquer output anterior
    if (ob_get_level()) {
        ob_clean();
    }
    
    showError($e->getMessage());
} catch (Throwable $e) {
    log_debug("Fatal error: " . $e->getMessage());
    
    if (ob_get_level()) {
        ob_clean();
    }
    
    showError("Erro interno: " . $e->getMessage());
}

function generateXmlFromJson($data) {
    $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    $xml .= '<nfeProc versao="' . htmlspecialchars($data['versao'] ?? '4.00') . '" xmlns="http://www.portalfiscal.inf.br/nfe">' . "\n";
    
    // NFe
    $xml .= '  <NFe xmlns="http://www.portalfiscal.inf.br/nfe">' . "\n";
    $xml .= '    <infNFe Id="' . htmlspecialchars($data['nfe']['id'] ?? '') . '" versao="' . htmlspecialchars($data['nfe']['versao'] ?? '4.00') . '">' . "\n";
    
    // Identificação
    $xml .= '      <ide>' . "\n";
    $xml .= '        <cUF>35</cUF>' . "\n";
    $xml .= '        <cNF>07468111</cNF>' . "\n";
    $xml .= '        <natOp>Venda</natOp>' . "\n";
    $xml .= '        <mod>55</mod>' . "\n";
    $xml .= '        <serie>1</serie>' . "\n";
    $xml .= '        <nNF>1</nNF>' . "\n";
    $xml .= '        <dhEmi>' . date('c') . '</dhEmi>' . "\n";
    $xml .= '        <tpNF>1</tpNF>' . "\n";
    $xml .= '        <idDest>1</idDest>' . "\n";
    $xml .= '        <cMunFG>3550308</cMunFG>' . "\n";
    $xml .= '        <tpImp>1</tpImp>' . "\n";
    $xml .= '        <tpEmis>1</tpEmis>' . "\n";
    $xml .= '        <cDV>7</cDV>' . "\n";
    $xml .= '        <tpAmb>2</tpAmb>' . "\n";
    $xml .= '        <finNFe>1</finNFe>' . "\n";
    $xml .= '        <indFinal>1</indFinal>' . "\n";
    $xml .= '        <indPres>1</indPres>' . "\n";
    $xml .= '      </ide>' . "\n";
    
    // Emitente básico
    $xml .= '      <emit>' . "\n";
    $xml .= '        <CNPJ>14200166000187</CNPJ>' . "\n";
    $xml .= '        <xNome>Empresa Exemplo</xNome>' . "\n";
    $xml .= '        <enderEmit>' . "\n";
    $xml .= '          <xLgr>Rua Exemplo</xLgr>' . "\n";
    $xml .= '          <nro>123</nro>' . "\n";
    $xml .= '          <xBairro>Centro</xBairro>' . "\n";
    $xml .= '          <cMun>3550308</cMun>' . "\n";
    $xml .= '          <xMun>São Paulo</xMun>' . "\n";
    $xml .= '          <UF>SP</UF>' . "\n";
    $xml .= '          <CEP>01000000</CEP>' . "\n";
    $xml .= '        </enderEmit>' . "\n";
    $xml .= '        <IE>123456789012</IE>' . "\n";
    $xml .= '        <CRT>3</CRT>' . "\n";
    $xml .= '      </emit>' . "\n";
    
    // Destinatário básico
    $xml .= '      <dest>' . "\n";
    $xml .= '        <CPF>12345678901</CPF>' . "\n";
    $xml .= '        <xNome>Cliente Exemplo</xNome>' . "\n";
    $xml .= '      </dest>' . "\n";
    
    // Itens (se existirem)
    if (isset($data['nfe']['itens']) && is_array($data['nfe']['itens'])) {
        foreach ($data['nfe']['itens'] as $index => $item) {
            $nItem = $index + 1;
            $xml .= '      <det nItem="' . $nItem . '">' . "\n";
            $xml .= '        <prod>' . "\n";
            $xml .= '          <cProd>' . htmlspecialchars($item['codigo'] ?? 'PROD' . $nItem) . '</cProd>' . "\n";
            $xml .= '          <cEAN></cEAN>' . "\n";
            $xml .= '          <xProd>' . htmlspecialchars($item['descricao'] ?? 'Produto ' . $nItem) . '</xProd>' . "\n";
            $xml .= '          <NCM>12345678</NCM>' . "\n";
            $xml .= '          <CFOP>5102</CFOP>' . "\n";
            $xml .= '          <uCom>UN</uCom>' . "\n";
            $xml .= '          <qCom>' . number_format($item['quantidade'] ?? 1, 4, '.', '') . '</qCom>' . "\n";
            $xml .= '          <vUnCom>' . number_format($item['valor_unitario'] ?? 0, 2, '.', '') . '</vUnCom>' . "\n";
            $xml .= '          <vProd>' . number_format($item['valor_total'] ?? 0, 2, '.', '') . '</vProd>' . "\n";
            $xml .= '          <cEANTrib></cEANTrib>' . "\n";
            $xml .= '          <uTrib>UN</uTrib>' . "\n";
            $xml .= '          <qTrib>' . number_format($item['quantidade'] ?? 1, 4, '.', '') . '</qTrib>' . "\n";
            $xml .= '          <vUnTrib>' . number_format($item['valor_unitario'] ?? 0, 2, '.', '') . '</vUnTrib>' . "\n";
            $xml .= '        </prod>' . "\n";
            $xml .= '        <imposto>' . "\n";
            $xml .= '          <ICMS>' . "\n";
            $xml .= '            <ICMS00>' . "\n";
            $xml .= '              <orig>0</orig>' . "\n";
            $xml .= '              <CST>00</CST>' . "\n";
            $xml .= '              <modBC>0</modBC>' . "\n";
            $xml .= '              <vBC>0.00</vBC>' . "\n";
            $xml .= '              <pICMS>0.00</pICMS>' . "\n";
            $xml .= '              <vICMS>0.00</vICMS>' . "\n";
            $xml .= '            </ICMS00>' . "\n";
            $xml .= '          </ICMS>' . "\n";
            $xml .= '        </imposto>' . "\n";
            $xml .= '      </det>' . "\n";
        }
    }
    
    // Total
    $xml .= '      <total>' . "\n";
    $xml .= '        <ICMSTot>' . "\n";
    $xml .= '          <vBC>0.00</vBC>' . "\n";
    $xml .= '          <vICMS>0.00</vICMS>' . "\n";
    $xml .= '          <vICMSDeson>0.00</vICMSDeson>' . "\n";
    $xml .= '          <vBCST>0.00</vBCST>' . "\n";
    $xml .= '          <vST>0.00</vST>' . "\n";
    $xml .= '          <vProd>100.00</vProd>' . "\n";
    $xml .= '          <vFrete>0.00</vFrete>' . "\n";
    $xml .= '          <vSeg>0.00</vSeg>' . "\n";
    $xml .= '          <vDesc>0.00</vDesc>' . "\n";
    $xml .= '          <vII>0.00</vII>' . "\n";
    $xml .= '          <vIPI>0.00</vIPI>' . "\n";
    $xml .= '          <vPIS>0.00</vPIS>' . "\n";
    $xml .= '          <vCOFINS>0.00</vCOFINS>' . "\n";
    $xml .= '          <vOutro>0.00</vOutro>' . "\n";
    $xml .= '          <vNF>100.00</vNF>' . "\n";
    $xml .= '        </ICMSTot>' . "\n";
    $xml .= '      </total>' . "\n";
    
    $xml .= '    </infNFe>' . "\n";
    $xml .= '  </NFe>' . "\n";
    
    // Protocolo
    $protocolo = $data['protocolo'];
    $xml .= '  <protNFe versao="' . htmlspecialchars($protocolo['versao'] ?? '4.00') . '">' . "\n";
    $xml .= '    <infProt>' . "\n";
    $xml .= '      <tpAmb>2</tpAmb>' . "\n";
    $xml .= '      <verAplic>SP_NFE_PL_008i2</verAplic>' . "\n";
    $xml .= '      <chNFe>' . htmlspecialchars($protocolo['chave_nfe'] ?? '') . '</chNFe>' . "\n";
    $xml .= '      <dhRecbto>' . date('c') . '</dhRecbto>' . "\n";
    $xml .= '      <nProt>' . htmlspecialchars($protocolo['numero_protocolo'] ?? '') . '</nProt>' . "\n";
    $xml .= '      <digVal>' . base64_encode('exemplo') . '</digVal>' . "\n";
    $xml .= '      <cStat>' . htmlspecialchars($protocolo['codigo_status'] ?? '100') . '</cStat>' . "\n";
    $xml .= '      <xMotivo>' . htmlspecialchars($protocolo['motivo'] ?? 'Autorizado o uso da NF-e') . '</xMotivo>' . "\n";
    $xml .= '    </infProt>' . "\n";
    $xml .= '  </protNFe>' . "\n";
    
    $xml .= '</nfeProc>';
    
    return $xml;
}

function showForm() {
    header('Content-Type: text/html; charset=UTF-8');
    echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>JSON para XML - Container</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; background: #f8f9fa; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        input, textarea { width: 100%; padding: 12px; margin: 10px 0; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        textarea { height: 200px; font-family: "Courier New", monospace; font-size: 14px; }
        button { background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; width: 100%; }
        button:hover { background: #218838; }
        .success { background: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; border-radius: 4px; margin: 20px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border: 1px solid #bee5eb; border-radius: 4px; margin: 20px 0; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 JSON para XML - Docker Container</h1>
        
        <div class="success">
            ✅ <strong>Versão Container:</strong> Otimizada para Docker, converte JSON procNfe para XML.
        </div>
        
        <form method="POST" enctype="multipart/form-data">
            <label><strong>📁 Upload arquivo JSON:</strong></label>
            <input type="file" name="json_file" accept=".json">
            
            <label><strong>📝 Ou cole o JSON:</strong></label>
            <textarea name="json_content" placeholder="Cole seu JSON aqui..."></textarea>
            
            <button type="submit">🚀 Converter para XML</button>
        </form>
        
        <div class="info">
            <strong>💡 Estrutura esperada:</strong> JSON com seções "nfe" e "protocolo"
        </div>
    </div>
</body>
</html>';
}

function showError($message) {
    header('Content-Type: text/html; charset=UTF-8');
    echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Erro - JSON para XML</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; background: #f8f9fa; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .error { background: #f8d7da; color: #721c24; padding: 20px; border: 1px solid #f5c6cb; border-radius: 4px; }
        .btn { background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; margin-top: 15px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="error">
            <h2>❌ Erro na Conversão</h2>
            <p><strong>Erro:</strong> ' . htmlspecialchars($message) . '</p>
        </div>
        <a href="javascript:history.back()" class="btn">⬅️ Voltar</a>
    </div>
</body>
</html>';
}
?>
