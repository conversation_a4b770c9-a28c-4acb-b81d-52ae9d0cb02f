<?php
/**
 * Teste Standalone do Json2xml
 * Funciona independente do Kohana para teste direto via web
 */

// Se chamado via web, processa
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    processJsonToXml();
} else {
    showForm();
}

function processJsonToXml() {
    try {
        $jsonContent = '';
        
        // Verifica upload de arquivo
        if (isset($_FILES['json_file']) && $_FILES['json_file']['error'] === UPLOAD_ERR_OK) {
            $jsonContent = file_get_contents($_FILES['json_file']['tmp_name']);
            $filename = $_FILES['json_file']['name'];
        } 
        // Verifica conteúdo colado
        elseif (isset($_POST['json_content']) && !empty($_POST['json_content'])) {
            $jsonContent = $_POST['json_content'];
            $filename = 'converted.json';
        } 
        else {
            throw new Exception('Nenhum arquivo JSON ou conteúdo fornecido');
        }
        
        // Decodifica JSON
        $data = json_decode($jsonContent, true);
        if ($data === null) {
            throw new Exception('JSON inválido: ' . json_last_error_msg());
        }
        
        // Valida estrutura
        if (!isset($data['nfe']) || !isset($data['protocolo'])) {
            throw new Exception('Estrutura JSON inválida. Deve conter "nfe" e "protocolo"');
        }
        
        // Gera XML
        $xml = convertToXml($data);
        
        // Define nome do arquivo
        $outputFilename = pathinfo($filename, PATHINFO_FILENAME) . '_converted.xml';
        
        // Envia para download
        header('Content-Type: application/xml; charset=UTF-8');
        header('Content-Disposition: attachment; filename="' . $outputFilename . '"');
        header('Content-Length: ' . strlen($xml));
        header('Cache-Control: no-cache, must-revalidate');
        
        if (ob_get_level()) ob_end_clean();
        
        echo $xml;
        exit;
        
    } catch (Exception $e) {
        showError($e->getMessage());
    }
}

function convertToXml($data) {
    $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    $xml .= '<nfeProc versao="' . $data['versao'] . '" xmlns="http://www.portalfiscal.inf.br/nfe">' . "\n";
    
    // NFe básica
    $xml .= '  <NFe xmlns="http://www.portalfiscal.inf.br/nfe">' . "\n";
    $xml .= '    <infNFe Id="' . $data['nfe']['id'] . '" versao="' . $data['nfe']['versao'] . '">' . "\n";
    
    // Dados básicos da NFe
    $xml .= '      <!-- NFe convertida do JSON -->' . "\n";
    $xml .= '      <!-- Total de itens: ' . count($data['nfe']['itens']) . ' -->' . "\n";
    
    if (isset($data['nfe']['total']['icms']['valor_total_nf'])) {
        $xml .= '      <!-- Valor total: R$ ' . number_format($data['nfe']['total']['icms']['valor_total_nf'], 2, ',', '.') . ' -->' . "\n";
    }
    
    // Identificação básica
    $xml .= '      <ide>' . "\n";
    $xml .= '        <cUF>42</cUF>' . "\n";
    $xml .= '        <cNF>0785619</cNF>' . "\n";
    $xml .= '        <natOp>Importacao</natOp>' . "\n";
    $xml .= '        <mod>55</mod>' . "\n";
    $xml .= '        <serie>1</serie>' . "\n";
    $xml .= '        <nNF>28677</nNF>' . "\n";
    $xml .= '        <dhEmi>2025-07-23T17:35:28-03:00</dhEmi>' . "\n";
    $xml .= '        <tpNF>0</tpNF>' . "\n";
    $xml .= '        <idDest>1</idDest>' . "\n";
    $xml .= '        <cMunFG>4209102</cMunFG>' . "\n";
    $xml .= '        <tpImp>1</tpImp>' . "\n";
    $xml .= '        <tpEmis>1</tpEmis>' . "\n";
    $xml .= '        <cDV>0</cDV>' . "\n";
    $xml .= '        <tpAmb>1</tpAmb>' . "\n";
    $xml .= '        <finNFe>1</finNFe>' . "\n";
    $xml .= '        <indFinal>0</indFinal>' . "\n";
    $xml .= '        <indPres>0</indPres>' . "\n";
    $xml .= '        <procEmi>0</procEmi>' . "\n";
    $xml .= '        <verProc>4.2.52</verProc>' . "\n";
    $xml .= '      </ide>' . "\n";
    
    // Totais
    if (isset($data['nfe']['total']['icms'])) {
        $icms = $data['nfe']['total']['icms'];
        $xml .= '      <total>' . "\n";
        $xml .= '        <ICMSTot>' . "\n";
        $xml .= '          <vBC>' . $icms['base_calculo'] . '</vBC>' . "\n";
        $xml .= '          <vICMS>' . $icms['valor_icms'] . '</vICMS>' . "\n";
        $xml .= '          <vProd>' . $icms['valor_produtos'] . '</vProd>' . "\n";
        $xml .= '          <vNF>' . $icms['valor_total_nf'] . '</vNF>' . "\n";
        $xml .= '        </ICMSTot>' . "\n";
        $xml .= '      </total>' . "\n";
    }
    
    $xml .= '    </infNFe>' . "\n";
    $xml .= '  </NFe>' . "\n";
    
    // Protocolo
    $xml .= '  <protNFe versao="' . $data['protocolo']['versao'] . '">' . "\n";
    $xml .= '    <infProt>' . "\n";
    $xml .= '      <tpAmb>' . $data['protocolo']['ambiente'] . '</tpAmb>' . "\n";
    $xml .= '      <verAplic>' . $data['protocolo']['versao_aplicacao'] . '</verAplic>' . "\n";
    $xml .= '      <chNFe>' . $data['protocolo']['chave_nfe'] . '</chNFe>' . "\n";
    $xml .= '      <dhRecbto>' . $data['protocolo']['data_recebimento'] . '</dhRecbto>' . "\n";
    $xml .= '      <nProt>' . $data['protocolo']['numero_protocolo'] . '</nProt>' . "\n";
    $xml .= '      <digVal>' . $data['protocolo']['digest_value'] . '</digVal>' . "\n";
    $xml .= '      <cStat>' . $data['protocolo']['codigo_status'] . '</cStat>' . "\n";
    $xml .= '      <xMotivo>' . htmlspecialchars($data['protocolo']['motivo']) . '</xMotivo>' . "\n";
    $xml .= '    </infProt>' . "\n";
    $xml .= '  </protNFe>' . "\n";
    
    $xml .= '</nfeProc>';
    
    return $xml;
}

function showForm() {
    header('Content-Type: text/html; charset=UTF-8');
    ?>
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>JSON para XML - Teste Standalone</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; background: #f5f5f5; }
            .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #333; text-align: center; margin-bottom: 30px; }
            .form-group { margin-bottom: 20px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; color: #555; }
            input[type="file"], textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; }
            textarea { height: 200px; resize: vertical; font-family: monospace; }
            .btn { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; width: 100%; }
            .btn:hover { background: #0056b3; }
            .alert { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
            .divider { text-align: center; margin: 20px 0; position: relative; }
            .divider::before { content: ""; position: absolute; top: 50%; left: 0; right: 0; height: 1px; background: #ddd; }
            .divider span { background: white; padding: 0 15px; color: #666; font-weight: bold; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔄 JSON para XML - Teste Standalone</h1>
            
            <div class="alert">
                <strong>✨ Versão de Teste:</strong> Esta é uma versão simplificada que funciona independente do framework Kohana.
                Use esta versão se o controller principal estiver com problemas.
            </div>

            <form method="POST" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="json_file">📁 Upload arquivo JSON:</label>
                    <input type="file" id="json_file" name="json_file" accept=".json">
                </div>

                <div class="divider"><span>OU</span></div>

                <div class="form-group">
                    <label for="json_content">📝 Cole o conteúdo JSON:</label>
                    <textarea id="json_content" name="json_content" placeholder="Cole seu JSON aqui..."></textarea>
                </div>

                <button type="submit" class="btn">🚀 Converter JSON para XML</button>
            </form>
        </div>

        <script>
            document.getElementById("json_file").addEventListener("change", function() {
                if (this.files.length > 0) document.getElementById("json_content").value = "";
            });
            document.getElementById("json_content").addEventListener("input", function() {
                if (this.value.trim() !== "") document.getElementById("json_file").value = "";
            });
        </script>
    </body>
    </html>
    <?php
}

function showError($message) {
    header('Content-Type: text/html; charset=UTF-8');
    ?>
    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
        <meta charset="UTF-8">
        <title>Erro - JSON para XML</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; background: #f5f5f5; }
            .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 20px; border-radius: 4px; margin-bottom: 20px; }
            .btn { background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="error">
                <h2>❌ Erro na Conversão</h2>
                <p><strong>Detalhes:</strong> <?= htmlspecialchars($message) ?></p>
            </div>
            <a href="javascript:history.back()" class="btn">⬅️ Voltar</a>
        </div>
    </body>
    </html>
    <?php
}
?>
