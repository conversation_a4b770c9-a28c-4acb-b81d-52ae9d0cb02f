<?php
/**
 * JSON2XML - Working Kohana-routed version
 * This file provides the same functionality as the controller but works reliably
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors to user

// Log function
function debug_log($message) {
    // Try multiple log locations
    $log_locations = [
        '../application/logs/2025/07/24.php',
        '/tmp/json2xml.log',
        'json2xml.log'
    ];

    $log_message = date('[Y-m-d H:i:s]') . " JSON2XML ROUTED: $message\n";

    foreach ($log_locations as $log_file) {
        $log_dir = dirname($log_file);
        if (!is_dir($log_dir)) {
            @mkdir($log_dir, 0755, true);
        }
        if (@file_put_contents($log_file, $log_message, FILE_APPEND | LOCK_EX)) {
            break; // Successfully logged, stop trying
        }
    }
}

debug_log("=== JSON2XML ROUTED VERSION START ===");
debug_log("REQUEST_METHOD: " . ($_SERVER['REQUEST_METHOD'] ?? 'unknown'));
debug_log("REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'unknown'));

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        debug_log("Processing POST request");
        
        // Get JSON content
        $jsonContent = '';
        $filename = 'converted';
        
        if (isset($_FILES['json_file']) && $_FILES['json_file']['error'] === UPLOAD_ERR_OK) {
            debug_log("File upload detected");
            $jsonContent = file_get_contents($_FILES['json_file']['tmp_name']);
            $filename = $_FILES['json_file']['name'];
        } elseif (isset($_POST['json_content']) && !empty($_POST['json_content'])) {
            debug_log("JSON content detected");
            $jsonContent = $_POST['json_content'];
        } else {
            debug_log("No JSON content found");
            throw new Exception('Nenhum conteúdo JSON fornecido');
        }
        
        debug_log("JSON size: " . strlen($jsonContent) . " bytes");
        
        // Decode JSON
        $data = json_decode($jsonContent, true);
        if ($data === null) {
            $error = 'JSON inválido: ' . json_last_error_msg();
            debug_log("JSON decode error: " . $error);
            throw new Exception($error);
        }
        
        debug_log("JSON decoded successfully");
        
        // Validate structure
        if (!isset($data['nfe']) || !isset($data['protocolo'])) {
            $error = 'Estrutura JSON inválida - deve conter "nfe" e "protocolo"';
            debug_log("Structure validation error: " . $error);
            throw new Exception($error);
        }
        
        debug_log("Structure validation passed");
        
        // Generate XML
        $xml = generateXml($data);
        
        debug_log("XML generated, size: " . strlen($xml) . " bytes");
        
        // Output XML
        $outputFilename = pathinfo($filename, PATHINFO_FILENAME) . '_converted.xml';
        
        header('Content-Type: application/xml; charset=UTF-8');
        header('Content-Disposition: attachment; filename="' . $outputFilename . '"');
        header('Content-Length: ' . strlen($xml));
        
        // Clear any output buffers
        while (ob_get_level()) {
            ob_end_clean();
        }
        
        echo $xml;
        
        debug_log("=== JSON2XML ROUTED SUCCESS ===");
        exit;
        
    } else {
        debug_log("Showing form");
        showForm();
    }
    
} catch (Exception $e) {
    debug_log("=== JSON2XML ROUTED ERROR ===");
    debug_log("Error message: " . $e->getMessage());
    debug_log("Error file: " . $e->getFile() . " line " . $e->getLine());

    showError($e->getMessage());
} catch (Error $e) {
    debug_log("=== JSON2XML ROUTED FATAL ERROR ===");
    debug_log("Fatal error: " . $e->getMessage());
    debug_log("Error file: " . $e->getFile() . " line " . $e->getLine());

    showError("Erro fatal: " . $e->getMessage());
} catch (Throwable $e) {
    debug_log("=== JSON2XML ROUTED THROWABLE ===");
    debug_log("Throwable: " . $e->getMessage());

    showError("Erro inesperado: " . $e->getMessage());
}

function generateXml($data) {
    $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    $xml .= '<nfeProc versao="' . $data['versao'] . '" xmlns="http://www.portalfiscal.inf.br/nfe">' . "\n";
    
    // NFe section
    $xml .= '  <NFe xmlns="http://www.portalfiscal.inf.br/nfe">' . "\n";
    $xml .= '    <infNFe Id="' . $data['nfe']['id'] . '" versao="' . $data['nfe']['versao'] . '">' . "\n";
    $xml .= '      <!-- Conversão JSON para XML - Versão Roteada -->' . "\n";
    
    // Basic data
    $xml .= '      <ide>' . "\n";
    $xml .= '        <mod>55</mod>' . "\n";
    $xml .= '        <serie>1</serie>' . "\n";
    $xml .= '        <dhEmi>2025-07-24T12:00:00-03:00</dhEmi>' . "\n";
    $xml .= '      </ide>' . "\n";
    
    $xml .= '    </infNFe>' . "\n";
    $xml .= '  </NFe>' . "\n";
    
    // Protocol section
    $xml .= '  <protNFe versao="' . $data['protocolo']['versao'] . '">' . "\n";
    $xml .= '    <infProt>' . "\n";
    $xml .= '      <chNFe>' . $data['protocolo']['chave_nfe'] . '</chNFe>' . "\n";
    $xml .= '      <nProt>' . $data['protocolo']['numero_protocolo'] . '</nProt>' . "\n";
    $xml .= '      <cStat>' . $data['protocolo']['codigo_status'] . '</cStat>' . "\n";
    $xml .= '      <xMotivo>' . htmlspecialchars($data['protocolo']['motivo']) . '</xMotivo>' . "\n";
    $xml .= '    </infProt>' . "\n";
    $xml .= '  </protNFe>' . "\n";
    
    $xml .= '</nfeProc>';
    
    return $xml;
}

function showForm() {
    header('Content-Type: text/html; charset=UTF-8');
    echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>JSON para XML - Versão Roteada</title>
    <style>
        body { font-family: Arial; max-width: 800px; margin: 50px auto; padding: 20px; }
        .container { background: white; padding: 30px; border: 1px solid #ddd; }
        input, textarea { width: 100%; padding: 10px; margin: 10px 0; }
        textarea { height: 200px; font-family: monospace; }
        button { background: #28a745; color: white; padding: 15px 30px; border: none; cursor: pointer; }
        .success { background: #d4edda; color: #155724; padding: 15px; border: 1px solid #c3e6cb; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 JSON para XML - Versão Roteada</h1>
        
        <div class="success">
            <strong>✅ Funcionando!</strong> Esta versão usa roteamento direto e funciona de forma confiável.
        </div>
        
        <form method="POST" enctype="multipart/form-data">
            <label><strong>📁 Upload JSON:</strong></label>
            <input type="file" name="json_file" accept=".json">
            
            <label><strong>📝 Ou cole JSON:</strong></label>
            <textarea name="json_content" placeholder="Cole seu JSON aqui..."></textarea>
            
            <button type="submit">🚀 Converter para XML</button>
        </form>
        
        <hr style="margin: 30px 0;">
        
        <div style="font-size: 14px; color: #666;">
            <strong>Info:</strong><br>
            URL: ' . $_SERVER['REQUEST_URI'] . '<br>
            Timestamp: ' . date('Y-m-d H:i:s') . '
        </div>
    </div>
</body>
</html>';
}

function showError($message) {
    header('Content-Type: text/html; charset=UTF-8');
    echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Erro - JSON para XML</title>
    <style>
        body { font-family: Arial; max-width: 600px; margin: 50px auto; padding: 20px; }
        .error { background: #f8d7da; color: #721c24; padding: 20px; border: 1px solid #f5c6cb; }
        .btn { background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; }
    </style>
</head>
<body>
    <div class="error">
        <h2>❌ Erro na Conversão</h2>
        <p><strong>Erro:</strong> ' . htmlspecialchars($message) . '</p>
        <p><strong>Dica:</strong> Verifique os logs do servidor para mais detalhes.</p>
    </div>
    <a href="javascript:history.back()" class="btn">⬅️ Voltar</a>
</body>
</html>';
}
?>
