<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test JSON2XML Controller</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        textarea {
            width: 100%;
            height: 200px;
            font-family: monospace;
            font-size: 12px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        input[type="file"] {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test JSON2XML Controller</h1>
        
        <div class="info">
            <strong>Instruções:</strong> Use os formulários abaixo para testar as diferentes versões do conversor JSON para XML.
        </div>

        <!-- Test 1: Working Routed Version -->
        <div class="test-section">
            <h3>1. ✅ Working Routed Version (RECOMMENDED)</h3>
            <p><strong>URL:</strong> <code>/metrics/json2xml.php</code></p>

            <form action="/metrics/json2xml.php" method="POST" enctype="multipart/form-data">
                <label><strong>📁 Upload JSON File:</strong></label>
                <input type="file" name="json_file" accept=".json">

                <label><strong>📝 Or paste JSON content:</strong></label>
                <textarea name="json_content" placeholder="Cole o JSON aqui..."></textarea>

                <button type="submit">🚀 Convert with Routed Version</button>
            </form>
        </div>

        <!-- Test 2: Kohana Controller -->
        <div class="test-section">
            <h3>2. 🎯 Kohana Controller (Troubleshooting)</h3>
            <p><strong>URL:</strong> <code>/metrics/json2xml/</code></p>

            <form action="/metrics/json2xml/" method="POST" enctype="multipart/form-data">
                <label><strong>📁 Upload JSON File:</strong></label>
                <input type="file" name="json_file" accept=".json">

                <label><strong>📝 Or paste JSON content:</strong></label>
                <textarea name="json_content" placeholder="Cole o JSON aqui..."></textarea>

                <button type="submit">🚀 Test Kohana Controller</button>
            </form>
        </div>

        <!-- Test 3: Standalone Version -->
        <div class="test-section">
            <h3>3. 🔧 Standalone Version</h3>
            <p><strong>URL:</strong> <code>/metrics/json2xml_standalone.php</code></p>
            
            <form action="/metrics/json2xml_standalone.php" method="POST" enctype="multipart/form-data">
                <label><strong>📁 Upload JSON File:</strong></label>
                <input type="file" name="json_file" accept=".json">
                
                <label><strong>📝 Or paste JSON content:</strong></label>
                <textarea name="json_content" placeholder="Cole o JSON aqui..."></textarea>
                
                <button type="submit">🚀 Test Standalone</button>
            </form>
        </div>

        <!-- Test 4: Simple Version -->
        <div class="test-section">
            <h3>4. 🎨 Simple Version</h3>
            <p><strong>URL:</strong> <code>/metrics/json2xml_simple.php</code></p>
            
            <form action="/metrics/json2xml_simple.php" method="POST" enctype="multipart/form-data">
                <label><strong>📁 Upload JSON File:</strong></label>
                <input type="file" name="json_file" accept=".json">
                
                <label><strong>📝 Or paste JSON content:</strong></label>
                <textarea name="json_content" placeholder="Cole o JSON aqui..."></textarea>
                
                <button type="submit">🚀 Test Simple</button>
            </form>
        </div>

        <!-- Test 5: Debug Version -->
        <div class="test-section">
            <h3>5. 🐛 Debug Version</h3>
            <p><strong>URL:</strong> <code>/metrics/json2xml_debug.php</code></p>
            
            <form action="/metrics/json2xml_debug.php" method="POST" enctype="multipart/form-data">
                <label><strong>📁 Upload JSON File:</strong></label>
                <input type="file" name="json_file" accept=".json">
                
                <label><strong>📝 Or paste JSON content:</strong></label>
                <textarea name="json_content" placeholder="Cole o JSON aqui..."></textarea>
                
                <button type="submit">🚀 Test Debug</button>
            </form>
        </div>

        <!-- Sample JSON -->
        <div class="test-section">
            <h3>6. 📋 Sample JSON for Testing</h3>
            <p>Copy this sample JSON to test the converters:</p>
            <textarea readonly>{
    "versao": "4.00",
    "nfe": {
        "id": "NFe42250701103171000613550010000286771070856190",
        "versao": "4.00",
        "itens": [
            {
                "numero_item": "1",
                "codigo_produto": "12345",
                "descricao": "Produto Teste",
                "quantidade": "1.0000",
                "valor_unitario": "100.00"
            }
        ]
    },
    "protocolo": {
        "versao": "4.00",
        "chave_nfe": "42250701103171000613550010000286771070856190",
        "numero_protocolo": "142250000123456",
        "codigo_status": "100",
        "motivo": "Autorizado o uso da NF-e"
    }
}</textarea>
        </div>

        <!-- Quick Links -->
        <div class="test-section">
            <h3>7. 🔗 Quick Test Links</h3>
            <p>Click these links to test GET requests:</p>
            <button onclick="window.open('/metrics/json2xml.php', '_blank')" style="background: #28a745;">✅ Test Working Version</button>
            <button onclick="window.open('/metrics/json2xml/test', '_blank')">Test Kohana /test</button>
            <button onclick="window.open('/metrics/json2xml/', '_blank')">Test Kohana Form</button>
            <button onclick="window.open('/metrics/json2xml_standalone.php', '_blank')">Test Standalone</button>
            <button onclick="window.open('/metrics/json2xml_simple.php', '_blank')">Test Simple</button>
        </div>
    </div>

    <script>
        // Auto-fill textareas with sample JSON when page loads
        document.addEventListener('DOMContentLoaded', function() {
            const sampleJson = document.querySelector('textarea[readonly]').value;
            const textareas = document.querySelectorAll('textarea:not([readonly])');
            
            textareas.forEach(textarea => {
                if (textarea.placeholder.includes('Cole')) {
                    textarea.value = sampleJson;
                }
            });
        });
    </script>
</body>
</html>
