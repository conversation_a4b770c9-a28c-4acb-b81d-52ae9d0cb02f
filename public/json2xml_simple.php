<?php
/**
 * JSON para XML - Versão Standalone SIMPLES
 * Sem logs, apenas conversão direta
 */

// Desabilita warnings para limpeza
error_reporting(E_ERROR | E_PARSE);

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Verifica se há conteúdo JSON
        $jsonContent = '';
        $filename = 'converted';
        
        if (isset($_FILES['json_file']) && $_FILES['json_file']['error'] === UPLOAD_ERR_OK) {
            $jsonContent = file_get_contents($_FILES['json_file']['tmp_name']);
            $filename = $_FILES['json_file']['name'];
        } elseif (isset($_POST['json_content']) && !empty($_POST['json_content'])) {
            $jsonContent = $_POST['json_content'];
        } else {
            throw new Exception('Nenhum conteúdo JSON fornecido');
        }
        
        // Testa decodificação básica
        $data = json_decode($jsonContent, true);
        if ($data === null) {
            throw new Exception('JSON inválido: ' . json_last_error_msg());
        }
        
        // Valida estrutura
        if (!isset($data['nfe']) || !isset($data['protocolo'])) {
            throw new Exception('Estrutura JSON inválida - requer seções "nfe" e "protocolo"');
        }
        
        // Gera XML simples
        $xml = generateSimpleXml($data);
        
        // Output direto
        $outputFilename = pathinfo($filename, PATHINFO_FILENAME) . '_converted.xml';
        
        // Headers
        header('Content-Type: application/xml; charset=UTF-8');
        header('Content-Disposition: attachment; filename="' . $outputFilename . '"');
        header('Content-Length: ' . strlen($xml));
        
        // Limpa buffer
        while (ob_get_level()) {
            ob_end_clean();
        }
        
        echo $xml;
        exit;
        
    } else {
        showSimpleForm();
    }
    
} catch (Exception $e) {
    showSimpleError($e->getMessage());
}

function generateSimpleXml($data) {
    $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    $xml .= '<nfeProc versao="' . htmlspecialchars($data['versao']) . '" xmlns="http://www.portalfiscal.inf.br/nfe">' . "\n";
    
    // NFe básica
    $xml .= '  <NFe xmlns="http://www.portalfiscal.inf.br/nfe">' . "\n";
    $xml .= '    <infNFe Id="' . htmlspecialchars($data['nfe']['id']) . '" versao="' . htmlspecialchars($data['nfe']['versao']) . '">' . "\n";
    $xml .= '      <!-- Conversão JSON para XML -->' . "\n";
    $xml .= '      <!-- Data: ' . date('Y-m-d H:i:s') . ' -->' . "\n";
    
    if (isset($data['nfe']['itens'])) {
        $xml .= '      <!-- Itens: ' . count($data['nfe']['itens']) . ' -->' . "\n";
    }
    
    // Dados básicos
    $xml .= '      <ide>' . "\n";
    $xml .= '        <mod>55</mod>' . "\n";
    $xml .= '        <serie>1</serie>' . "\n";
    $xml .= '        <dhEmi>' . date('c') . '</dhEmi>' . "\n";
    $xml .= '      </ide>' . "\n";
    
    $xml .= '    </infNFe>' . "\n";
    $xml .= '  </NFe>' . "\n";
    
    // Protocolo
    $xml .= '  <protNFe versao="' . htmlspecialchars($data['protocolo']['versao']) . '">' . "\n";
    $xml .= '    <infProt>' . "\n";
    $xml .= '      <chNFe>' . htmlspecialchars($data['protocolo']['chave_nfe']) . '</chNFe>' . "\n";
    $xml .= '      <nProt>' . htmlspecialchars($data['protocolo']['numero_protocolo']) . '</nProt>' . "\n";
    $xml .= '      <cStat>' . htmlspecialchars($data['protocolo']['codigo_status']) . '</cStat>' . "\n";
    $xml .= '      <xMotivo>' . htmlspecialchars($data['protocolo']['motivo']) . '</xMotivo>' . "\n";
    $xml .= '    </infProt>' . "\n";
    $xml .= '  </protNFe>' . "\n";
    
    $xml .= '</nfeProc>';
    
    return $xml;
}

function showSimpleForm() {
    header('Content-Type: text/html; charset=UTF-8');
    echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>JSON para XML - Conversor</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 800px; 
            margin: 50px auto; 
            padding: 20px; 
            background: #f8f9fa; 
        }
        .container { 
            background: white; 
            padding: 30px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        input, textarea { 
            width: 100%; 
            padding: 12px; 
            margin: 10px 0; 
            border: 1px solid #ddd; 
            border-radius: 4px; 
            box-sizing: border-box;
        }
        textarea { 
            height: 200px; 
            font-family: "Courier New", monospace; 
            font-size: 14px;
        }
        button { 
            background: #28a745; 
            color: white; 
            padding: 15px 30px; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
            font-size: 16px;
            width: 100%;
        }
        button:hover { 
            background: #218838; 
        }
        .success { 
            background: #d4edda; 
            color: #155724; 
            padding: 15px; 
            border: 1px solid #c3e6cb; 
            border-radius: 4px; 
            margin: 20px 0; 
        }
        .info { 
            background: #d1ecf1; 
            color: #0c5460; 
            padding: 15px; 
            border: 1px solid #bee5eb; 
            border-radius: 4px; 
            margin: 20px 0; 
            font-size: 14px;
        }
        .example {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 JSON para XML - Conversor NFe</h1>
        
        <div class="success">
            ✅ <strong>Conversor Funcionando!</strong> Esta versão standalone converte JSON de procNfe para XML.
        </div>
        
        <form method="POST" enctype="multipart/form-data">
            <label><strong>📁 Upload arquivo JSON:</strong></label>
            <input type="file" name="json_file" accept=".json">
            
            <label><strong>📝 Ou cole o JSON diretamente:</strong></label>
            <textarea name="json_content" placeholder="Cole seu JSON aqui..."></textarea>
            
            <button type="submit">🚀 Converter para XML</button>
        </form>
        
        <div class="info">
            <strong>💡 Estrutura esperada:</strong> O JSON deve conter as seções "nfe" e "protocolo" como no exemplo abaixo:
            <div class="example">{
  "versao": "4.00",
  "nfe": {
    "id": "NFe35200...",
    "versao": "4.00",
    "itens": [...]
  },
  "protocolo": {
    "versao": "4.00",
    "chave_nfe": "35200...",
    "numero_protocolo": "135200...",
    "codigo_status": "100",
    "motivo": "Autorizado o uso da NF-e"
  }
}</div>
        </div>
        
        <div style="font-size: 12px; color: #666; text-align: center; margin-top: 30px;">
            Servidor: ' . $_SERVER['SERVER_NAME'] . ' | PHP: ' . PHP_VERSION . ' | ' . date('Y-m-d H:i:s') . '
        </div>
    </div>
</body>
</html>';
}

function showSimpleError($message) {
    header('Content-Type: text/html; charset=UTF-8');
    echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Erro - JSON para XML</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 600px; 
            margin: 50px auto; 
            padding: 20px; 
            background: #f8f9fa; 
        }
        .container { 
            background: white; 
            padding: 30px; 
            border-radius: 8px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .error { 
            background: #f8d7da; 
            color: #721c24; 
            padding: 20px; 
            border: 1px solid #f5c6cb; 
            border-radius: 4px; 
        }
        .btn { 
            background: #6c757d; 
            color: white; 
            padding: 10px 20px; 
            text-decoration: none; 
            border-radius: 4px; 
            display: inline-block;
            margin-top: 15px;
        }
        .btn:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error">
            <h2>❌ Erro na Conversão</h2>
            <p><strong>Erro:</strong> ' . htmlspecialchars($message) . '</p>
            <p><strong>Dica:</strong> Verifique se o JSON tem as seções "nfe" e "protocolo".</p>
        </div>
        <a href="javascript:history.back()" class="btn">⬅️ Voltar</a>
    </div>
</body>
</html>';
}
?>
