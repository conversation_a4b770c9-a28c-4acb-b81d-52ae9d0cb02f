<?php
/**
 * Debug do Controller Json2xml
 * Testa se o controller funciona no ambiente de produção
 */

// Simula o ambiente do controller
$_SERVER['REQUEST_METHOD'] = 'POST';
$_POST['json_content'] = file_get_contents('/home/<USER>/environment/Office/Apps/inProduction/metrics/procnfe_array.json');

echo "🔍 Debug Controller Json2xml\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    // Simula uma requisição POST
    echo "📋 Teste de Requisição POST:\n";
    echo "   • REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD'] . "\n";
    echo "   • Tamanho JSON: " . strlen($_POST['json_content']) . " bytes\n";
    echo "   • JSON válido: " . (json_decode($_POST['json_content']) ? 'Sim' : 'Não') . "\n\n";
    
    // Testa decodificação JSON
    echo "📋 Teste de Decodificação JSON:\n";
    $data = json_decode($_POST['json_content'], true);
    
    if ($data === null) {
        throw new Exception('JSON inválido: ' . json_last_error_msg());
    }
    
    echo "   ✅ JSON decodificado com sucesso\n";
    echo "   • Campos principais: " . implode(', ', array_keys($data)) . "\n";
    
    if (isset($data['nfe'])) {
        echo "   • NFe ID: " . $data['nfe']['id'] . "\n";
        echo "   • Qtd Itens: " . count($data['nfe']['itens']) . "\n";
    }
    
    if (isset($data['protocolo'])) {
        echo "   • Protocolo: " . $data['protocolo']['numero_protocolo'] . "\n";
        echo "   • Status: " . $data['protocolo']['codigo_status'] . "\n";
    }
    
    echo "\n";
    
    // Testa validação básica
    echo "📋 Teste de Validação:\n";
    
    if (!isset($data['nfe']) || !isset($data['protocolo'])) {
        throw new Exception('Estrutura JSON inválida');
    }
    
    echo "   ✅ Estrutura básica válida\n";
    echo "   ✅ Campo 'nfe' presente\n";
    echo "   ✅ Campo 'protocolo' presente\n\n";
    
    // Testa geração XML básica
    echo "📋 Teste de Geração XML:\n";
    
    $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    $xml .= '<nfeProc versao="' . $data['versao'] . '" xmlns="http://www.portalfiscal.inf.br/nfe">' . "\n";
    $xml .= '  <!-- NFe convertida do JSON -->' . "\n";
    $xml .= '  <!-- ID: ' . $data['nfe']['id'] . ' -->' . "\n";
    $xml .= '  <!-- Itens: ' . count($data['nfe']['itens']) . ' -->' . "\n";
    $xml .= '</nfeProc>';
    
    echo "   ✅ XML básico gerado (" . strlen($xml) . " bytes)\n";
    
    // Valida XML
    $dom = new DOMDocument();
    if ($dom->loadXML($xml)) {
        echo "   ✅ XML válido\n";
    } else {
        echo "   ❌ XML inválido\n";
    }
    
    echo "\n📊 Resultado: CONTROLLER DEVE FUNCIONAR!\n\n";
    
    echo "🔧 Se ainda der erro 500:\n";
    echo "   1. Verifique permissões de arquivo\n";
    echo "   2. Verifique se as classes do Kohana estão carregadas\n";
    echo "   3. Verifique logs do Apache/Nginx\n";
    echo "   4. Teste com JSON menor primeiro\n\n";
    
    echo "🌐 URL para testar:\n";
    echo "   https://dev.office.internut.com.br/metrics/json2xml/\n\n";
    
} catch (Exception $e) {
    echo "❌ Erro: " . $e->getMessage() . "\n";
    
    echo "\n🔧 Possíveis soluções:\n";
    echo "   • Verifique sintaxe do JSON\n";
    echo "   • Verifique estrutura dos dados\n";
    echo "   • Teste com JSON menor\n";
}
