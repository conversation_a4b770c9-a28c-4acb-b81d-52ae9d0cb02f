<?php
/**
 * Exemplo de uso do Controller Json2xml
 * Demonstra como usar o conversor JSON para XML programaticamente
 */

// Não precisa do bootstrap para este exemplo

class ExemploJson2Xml {
    
    /**
     * Exemplo de conversão programática
     */
    public function exemploConversaoProgramatica() {
        try {
            // Carrega o JSON do arquivo procnfe_array.json
            $jsonFile = '/home/<USER>/environment/Office/Apps/inProduction/metrics/procnfe_array.json';
            
            if (!file_exists($jsonFile)) {
                throw new Exception("Arquivo JSON não encontrado: $jsonFile");
            }
            
            $jsonContent = file_get_contents($jsonFile);
            $data = json_decode($jsonContent, true);
            
            if ($data === null) {
                throw new Exception('JSON inválido: ' . json_last_error_msg());
            }
            
            echo "✅ JSON carregado com sucesso!\n";
            echo "📊 Estatísticas do JSON:\n";
            echo "   - Versão: " . $data['versao'] . "\n";
            echo "   - ID NFe: " . $data['nfe']['id'] . "\n";
            echo "   - Qtd Itens: " . count($data['nfe']['itens']) . "\n";
            echo "   - Valor Total: R$ " . number_format($data['nfe']['total']['icms']['valor_total_nf'], 2, ',', '.') . "\n";
            echo "   - Status Protocolo: " . $data['protocolo']['codigo_status'] . " - " . $data['protocolo']['motivo'] . "\n\n";
            
            // Simula a conversão (sem gerar arquivo real)
            echo "🔄 Iniciando conversão JSON → XML...\n";
            
            $converter = new Json2XmlConverter();
            $xmlContent = $converter->convertToXml($data);
            
            echo "✅ Conversão concluída!\n";
            echo "📄 XML gerado com " . strlen($xmlContent) . " caracteres\n";
            
            // Salva o XML em arquivo
            $outputFile = 'procnfe_converted_example.xml';
            file_put_contents($outputFile, $xmlContent);
            
            echo "💾 Arquivo XML salvo: $outputFile\n";
            
            return true;
            
        } catch (Exception $e) {
            echo "❌ Erro: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * Valida estrutura JSON
     */
    public function validarEstrutura($data) {
        $required = ['nfe', 'protocolo', 'versao'];
        
        foreach ($required as $field) {
            if (!isset($data[$field])) {
                throw new Exception("Campo obrigatório ausente: $field");
            }
        }
        
        if (!isset($data['nfe']['itens']) || !is_array($data['nfe']['itens'])) {
            throw new Exception("Campo 'nfe.itens' deve ser um array");
        }
        
        if (empty($data['nfe']['itens'])) {
            throw new Exception("NFe deve conter pelo menos um item");
        }
        
        return true;
    }
}

/**
 * Classe auxiliar para conversão JSON → XML
 */
class Json2XmlConverter {
    
    public function convertToXml($data) {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        
        // Namespace do procNfe
        $xml .= '<nfeProc versao="' . $data['versao'] . '" xmlns="http://www.portalfiscal.inf.br/nfe">' . "\n";
        
        // Adiciona a NFe (estrutura simplificada para exemplo)
        $xml .= $this->buildSimpleNfeXml($data['nfe']);
        
        // Adiciona o protocolo
        $xml .= $this->buildProtocoloXml($data['protocolo']);
        
        $xml .= '</nfeProc>';
        
        return $xml;
    }
    
    private function buildSimpleNfeXml($nfe) {
        $xml = '  <NFe xmlns="http://www.portalfiscal.inf.br/nfe">' . "\n";
        $xml .= '    <infNFe Id="' . $nfe['id'] . '" versao="' . $nfe['versao'] . '">' . "\n";
        
        // Dados básicos (exemplo simplificado)
        $xml .= '      <!-- Estrutura NFe completa seria gerada aqui -->' . "\n";
        $xml .= '      <!-- Total de itens: ' . count($nfe['itens']) . ' -->' . "\n";
        
        if (isset($nfe['total']['icms']['valor_total_nf'])) {
            $xml .= '      <!-- Valor total: R$ ' . $nfe['total']['icms']['valor_total_nf'] . ' -->' . "\n";
        }
        
        $xml .= '    </infNFe>' . "\n";
        $xml .= '  </NFe>' . "\n";
        
        return $xml;
    }
    
    private function buildProtocoloXml($protocolo) {
        $xml = '  <protNFe versao="' . $protocolo['versao'] . '">' . "\n";
        $xml .= '    <infProt>' . "\n";
        $xml .= '      <tpAmb>' . $protocolo['ambiente'] . '</tpAmb>' . "\n";
        $xml .= '      <verAplic>' . $protocolo['versao_aplicacao'] . '</verAplic>' . "\n";
        $xml .= '      <chNFe>' . $protocolo['chave_nfe'] . '</chNFe>' . "\n";
        $xml .= '      <dhRecbto>' . $protocolo['data_recebimento'] . '</dhRecbto>' . "\n";
        $xml .= '      <nProt>' . $protocolo['numero_protocolo'] . '</nProt>' . "\n";
        $xml .= '      <digVal>' . $protocolo['digest_value'] . '</digVal>' . "\n";
        $xml .= '      <cStat>' . $protocolo['codigo_status'] . '</cStat>' . "\n";
        $xml .= '      <xMotivo>' . htmlspecialchars($protocolo['motivo']) . '</xMotivo>' . "\n";
        $xml .= '    </infProt>' . "\n";
        $xml .= '  </protNFe>' . "\n";
        
        return $xml;
    }
}

// Executa o exemplo se o arquivo for chamado diretamente
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    echo "🚀 Exemplo de Conversão JSON → XML procNfe\n";
    echo "=" . str_repeat("=", 50) . "\n\n";
    
    $exemplo = new ExemploJson2Xml();
    $resultado = $exemplo->exemploConversaoProgramatica();
    
    echo "\n" . str_repeat("=", 50) . "\n";
    echo $resultado ? "✅ Exemplo executado com sucesso!" : "❌ Exemplo falhou!";
    echo "\n\n";
    
    echo "💡 Para usar via web:\n";
    echo "   Acesse: http://localhost/json2xml\n";
    echo "   Ou através da URL do seu servidor\n\n";
    
    echo "📚 Funcionalidades do Controller:\n";
    echo "   ✓ Upload de arquivo JSON\n";
    echo "   ✓ Cole conteúdo JSON diretamente\n";
    echo "   ✓ Conversão completa para XML procNfe\n";
    echo "   ✓ Download automático do XML\n";
    echo "   ✓ Validação de estrutura JSON\n";
    echo "   ✓ Tratamento de erros\n";
}
