<?php

require_once 'application/classes/ProcNfeParser.php';

try {
    // Caminho para o arquivo procNfe XML
    $xmlFile = '/home/<USER>/environment/Office/Apps/inProduction/metrics/application/views/42250701103171000613550010000286771070856190-procNfe.xml';
    
    echo "=== ENGENHARIA REVERSA - PROCNFE PARA ARRAY PHP ===\n\n";
    
    // Converte o XML para array PHP
    $procNfeArray = ProcNfeParser::parseXmlToArray($xmlFile);
    
    // Cria um resumo estruturado
    $resumo = array(
        'procnfe_info' => array(
            'versao' => $procNfeArray['versao'],
            'tipo_documento' => 'NFe processada com protocolo'
        ),
        'nfe_resumo' => array(
            'chave' => $procNfeArray['nfe']['id'],
            'numero' => $procNfeArray['nfe']['identificacao']['numero_nf'],
            'serie' => $procNfeArray['nfe']['identificacao']['serie'],
            'data_emissao' => $procNfeArray['nfe']['identificacao']['data_emissao'],
            'valor_total' => $procNfeArray['nfe']['total']['icms']['valor_total_nf'],
            'total_itens' => count($procNfeArray['nfe']['itens'])
        ),
        'partes' => array(
            'emitente' => array(
                'cnpj' => $procNfeArray['nfe']['emitente']['cnpj'],
                'nome' => $procNfeArray['nfe']['emitente']['nome'],
                'cidade' => $procNfeArray['nfe']['emitente']['endereco']['municipio'],
                'uf' => $procNfeArray['nfe']['emitente']['endereco']['uf']
            ),
            'destinatario' => array(
                'nome' => $procNfeArray['nfe']['destinatario']['nome'],
                'pais' => $procNfeArray['nfe']['destinatario']['endereco']['pais'],
                'tipo' => 'Empresa estrangeira'
            )
        ),
        'produtos_resumo' => array(),
        'totais_financeiros' => array(
            'produtos' => $procNfeArray['nfe']['total']['icms']['valor_produtos'],
            'frete' => $procNfeArray['nfe']['total']['icms']['valor_frete'],
            'seguro' => $procNfeArray['nfe']['total']['icms']['valor_seguro'],
            'outros' => $procNfeArray['nfe']['total']['icms']['valor_outros'],
            'impostos' => array(
                'ii' => $procNfeArray['nfe']['total']['icms']['valor_ii'],
                'pis' => $procNfeArray['nfe']['total']['icms']['valor_pis'],
                'cofins' => $procNfeArray['nfe']['total']['icms']['valor_cofins']
            ),
            'total_nf' => $procNfeArray['nfe']['total']['icms']['valor_total_nf']
        ),
        'protocolo_status' => array(
            'numero' => $procNfeArray['protocolo']['numero_protocolo'],
            'data_autorizacao' => $procNfeArray['protocolo']['data_recebimento'],
            'status' => $procNfeArray['protocolo']['codigo_status'],
            'situacao' => $procNfeArray['protocolo']['motivo']
        )
    );
    
    // Resume os produtos por tipo
    $tipos_produtos = array();
    foreach ($procNfeArray['nfe']['itens'] as $item) {
        $descricao = $item['produto']['descricao'];
        $ncm = $item['produto']['ncm'];
        
        if (!isset($tipos_produtos[$ncm])) {
            $tipos_produtos[$ncm] = array(
                'descricao_base' => $descricao,
                'quantidade_total' => 0,
                'valor_total' => 0,
                'itens' => 0
            );
        }
        
        $tipos_produtos[$ncm]['quantidade_total'] += (float) $item['produto']['quantidade_comercial'];
        $tipos_produtos[$ncm]['valor_total'] += (float) $item['produto']['valor_produto'];
        $tipos_produtos[$ncm]['itens']++;
    }
    
    $resumo['produtos_resumo'] = $tipos_produtos;
    
    // Salva o resumo
    $resumoFile = '/home/<USER>/environment/Office/Apps/inProduction/metrics/procnfe_resumo.json';
    file_put_contents($resumoFile, json_encode($resumo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    
    // Exibe o resumo
    echo "DOCUMENTO: procNfe (NFe + Protocolo)\n";
    echo "VERSÃO: " . $resumo['procnfe_info']['versao'] . "\n\n";
    
    echo "=== NFE ===\n";
    echo "Número: " . $resumo['nfe_resumo']['numero'] . " (Série " . $resumo['nfe_resumo']['serie'] . ")\n";
    echo "Data: " . $resumo['nfe_resumo']['data_emissao'] . "\n";
    echo "Valor Total: R$ " . number_format((float)$resumo['nfe_resumo']['valor_total'], 2, ',', '.') . "\n";
    echo "Total de Itens: " . $resumo['nfe_resumo']['total_itens'] . "\n\n";
    
    echo "=== PARTES ===\n";
    echo "Emitente: " . $resumo['partes']['emitente']['nome'] . "\n";
    echo "CNPJ: " . $resumo['partes']['emitente']['cnpj'] . "\n";
    echo "Local: " . $resumo['partes']['emitente']['cidade'] . "/" . $resumo['partes']['emitente']['uf'] . "\n\n";
    
    echo "Destinatário: " . $resumo['partes']['destinatario']['nome'] . "\n";
    echo "País: " . $resumo['partes']['destinatario']['pais'] . "\n\n";
    
    echo "=== PRODUTOS POR CATEGORIA (NCM) ===\n";
    foreach ($resumo['produtos_resumo'] as $ncm => $dados) {
        echo "NCM: $ncm\n";
        echo "  Tipo: " . substr($dados['descricao_base'], 0, 60) . "...\n";
        echo "  Quantidade de Linhas: " . $dados['itens'] . "\n";
        echo "  Quantidade Total: " . number_format($dados['quantidade_total'], 0, ',', '.') . " unidades\n";
        echo "  Valor Total: R$ " . number_format($dados['valor_total'], 2, ',', '.') . "\n\n";
    }
    
    echo "=== TOTAIS FINANCEIROS ===\n";
    echo "Produtos: R$ " . number_format((float)$resumo['totais_financeiros']['produtos'], 2, ',', '.') . "\n";
    echo "Frete: R$ " . number_format((float)$resumo['totais_financeiros']['frete'], 2, ',', '.') . "\n";
    echo "Seguro: R$ " . number_format((float)$resumo['totais_financeiros']['seguro'], 2, ',', '.') . "\n";
    echo "Outros: R$ " . number_format((float)$resumo['totais_financeiros']['outros'], 2, ',', '.') . "\n";
    echo "Imposto II: R$ " . number_format((float)$resumo['totais_financeiros']['impostos']['ii'], 2, ',', '.') . "\n";
    echo "PIS: R$ " . number_format((float)$resumo['totais_financeiros']['impostos']['pis'], 2, ',', '.') . "\n";
    echo "COFINS: R$ " . number_format((float)$resumo['totais_financeiros']['impostos']['cofins'], 2, ',', '.') . "\n";
    echo "TOTAL NFE: R$ " . number_format((float)$resumo['totais_financeiros']['total_nf'], 2, ',', '.') . "\n\n";
    
    echo "=== PROTOCOLO ===\n";
    echo "Número: " . $resumo['protocolo_status']['numero'] . "\n";
    echo "Data Autorização: " . $resumo['protocolo_status']['data_autorizacao'] . "\n";
    echo "Status: " . $resumo['protocolo_status']['status'] . " - " . $resumo['protocolo_status']['situacao'] . "\n\n";
    
    echo "=== ARQUIVOS GERADOS ===\n";
    echo "Array completo: /home/<USER>/environment/Office/Apps/inProduction/metrics/procnfe_array.json\n";
    echo "Resumo: $resumoFile\n\n";
    
    echo "=== ENGENHARIA REVERSA CONCLUÍDA ===\n";
    echo "✓ XML procNfe convertido para array PHP estruturado\n";
    echo "✓ " . count($procNfeArray['nfe']['itens']) . " itens de máquinas de costura processados\n";
    echo "✓ Informações fiscais e tributárias extraídas\n";
    echo "✓ Dados de importação (DI) capturados\n";
    echo "✓ Protocolo de autorização da Receita Federal incluído\n";
    echo "✓ Estrutura PHP pronta para uso em aplicações\n";
    
} catch (Exception $e) {
    echo "Erro: " . $e->getMessage() . "\n";
}
