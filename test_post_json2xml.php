<?php
/**
 * Test script to POST JSON to the json2xml controller
 */

// Read the JSON file
$jsonFile = '/home/<USER>/environment/Office/Apps/inProduction/metrics/procnfe_array.json';
$jsonContent = file_get_contents($jsonFile);

if (!$jsonContent) {
    die("Could not read JSON file\n");
}

echo "Testing JSON2XML Controller via POST\n";
echo "=====================================\n";
echo "JSON file size: " . strlen($jsonContent) . " bytes\n";

// Test URL
$url = 'https://dev.office.internut.com.br/metrics/json2xml/';

// Prepare POST data
$postData = array(
    'json_content' => $jsonContent
);

// Initialize cURL
$ch = curl_init();

// Set cURL options
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_VERBOSE, true);

// Capture verbose output
$verbose = fopen('php://temp', 'w+');
curl_setopt($ch, CURLOPT_STDERR, $verbose);

// Execute request
echo "\nSending POST request...\n";
$response = curl_exec($ch);

// Get request info
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);

// Get verbose output
rewind($verbose);
$verboseLog = stream_get_contents($verbose);

// Close cURL
curl_close($ch);

echo "\nResponse Details:\n";
echo "HTTP Code: $httpCode\n";
echo "Content Type: $contentType\n";
echo "Response Length: " . strlen($response) . " bytes\n";

if ($httpCode == 200) {
    echo "\n✅ SUCCESS! Controller responded correctly\n";
    
    // Check if it's XML
    if (strpos($contentType, 'xml') !== false || strpos($response, '<?xml') === 0) {
        echo "✅ Response appears to be XML\n";
        echo "XML Preview (first 200 chars):\n";
        echo substr($response, 0, 200) . "...\n";
    } else {
        echo "Response Preview (first 500 chars):\n";
        echo substr($response, 0, 500) . "...\n";
    }
} else {
    echo "\n❌ ERROR! HTTP Code: $httpCode\n";
    echo "Response:\n";
    echo $response . "\n";
    
    echo "\nVerbose Log:\n";
    echo $verboseLog . "\n";
}

echo "\n=== Test Complete ===\n";
?>
