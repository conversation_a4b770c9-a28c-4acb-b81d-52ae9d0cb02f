<?php

// Exemplo de uso prático do NfeParser
require_once 'application/classes/NfeParser.php';

$xmlPath = '/home/<USER>/environment/Office/Apps/inProduction/metrics/application/views/42250701103171000613550010000006051070856197-nfe.xml';

try {
    // Parse do XML para array
    $nfeArray = NfeParser::parseXmlToArray($xmlPath);
    
    echo "=== ESTRUTURA COMPLETA DO ARRAY ===\n\n";
    
    // Exemplo 1: Informações básicas da NFe
    echo "1. DADOS GERAIS:\n";
    echo json_encode($nfeArray['identificacao'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
    
    // Exemplo 2: Dados do emitente
    echo "2. EMITENTE:\n";
    echo json_encode($nfeArray['emitente'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
    
    // Exemplo 3: Dados do destinatário
    echo "3. DESTINATÁRIO:\n";
    echo json_encode($nfeArray['destinatario'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
    
    // Exemplo 4: Totais da NFe
    echo "4. TOTAIS:\n";
    echo json_encode($nfeArray['totais'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
    
    // Exemplo 5: Primeiro item detalhado
    echo "5. PRIMEIRO ITEM (DETALHADO):\n";
    if (!empty($nfeArray['itens'])) {
        echo json_encode($nfeArray['itens'][0], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
    }
    
    // Exemplo 6: Análise dos itens
    echo "6. ANÁLISE DOS ITENS:\n";
    $totalItens = count($nfeArray['itens']);
    $valorTotalProdutos = array_sum(array_column(array_column($nfeArray['itens'], 'produto'), 'valor_produto'));
    $quantidadeTotal = array_sum(array_column(array_column($nfeArray['itens'], 'produto'), 'quantidade_comercial'));
    
    echo "Total de itens: $totalItens\n";
    echo "Quantidade total de produtos: $quantidadeTotal\n";
    echo "Valor total dos produtos: R$ " . number_format($valorTotalProdutos, 2, ',', '.') . "\n\n";
    
    // Exemplo 7: Lista resumida de produtos
    echo "7. RESUMO DOS PRODUTOS:\n";
    foreach ($nfeArray['itens'] as $index => $item) {
        $produto = $item['produto'];
        echo sprintf(
            "Item %02d: %s | Qtd: %s | Valor: R$ %s\n",
            $index + 1,
            substr($produto['descricao'], 0, 60) . '...',
            number_format($produto['quantidade_comercial'], 0),
            number_format($produto['valor_produto'], 2, ',', '.')
        );
    }
    
    echo "\n8. DADOS DE TRANSPORTE:\n";
    echo json_encode($nfeArray['transporte'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
    
    echo "9. DADOS DE PAGAMENTO:\n";
    echo json_encode($nfeArray['pagamento'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
    
    // Exemplo 10: Exportar para JSON
    $jsonFile = '/tmp/nfe_parsed.json';
    file_put_contents($jsonFile, json_encode($nfeArray, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    echo "10. ARRAY COMPLETO EXPORTADO PARA: $jsonFile\n";
    
} catch (Exception $e) {
    echo "Erro: " . $e->getMessage() . "\n";
}
