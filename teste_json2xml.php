<?php
/**
 * Teste do Controller Json2xml
 * Verifica se a conversão JSON → XML está funcionando corretamente
 */

// Não precisa do bootstrap para este teste

echo "🧪 Teste do Controller Json2xml\n";
echo "=" . str_repeat("=", 50) . "\n\n";

try {
    // 1. Testa carregamento do JSON
    echo "📋 Teste 1: Carregamento do JSON\n";
    $jsonFile = '/home/<USER>/environment/Office/Apps/inProduction/metrics/procnfe_array.json';
    
    if (!file_exists($jsonFile)) {
        throw new Exception("❌ Arquivo JSON não encontrado");
    }
    
    $jsonContent = file_get_contents($jsonFile);
    $data = json_decode($jsonContent, true);
    
    if ($data === null) {
        throw new Exception("❌ JSON inválido: " . json_last_error_msg());
    }
    
    echo "   ✅ JSON carregado com sucesso (" . strlen($jsonContent) . " bytes)\n\n";
    
    // 2. Testa validação da estrutura
    echo "📋 Teste 2: Validação da estrutura\n";
    
    $required = ['nfe', 'protocolo', 'versao'];
    foreach ($required as $field) {
        if (!isset($data[$field])) {
            throw new Exception("❌ Campo obrigatório ausente: $field");
        }
        echo "   ✅ Campo '$field' presente\n";
    }
    
    if (!isset($data['nfe']['itens']) || !is_array($data['nfe']['itens'])) {
        throw new Exception("❌ Campo 'nfe.itens' inválido");
    }
    echo "   ✅ Campo 'nfe.itens' é array com " . count($data['nfe']['itens']) . " itens\n\n";
    
    // 3. Testa geração de XML básico
    echo "📋 Teste 3: Geração de XML\n";
    
    // Simula o método do controller
    $xml = generateTestXml($data);
    
    if (empty($xml)) {
        throw new Exception("❌ XML vazio gerado");
    }
    
    // Verifica se é XML válido
    $dom = new DOMDocument();
    $dom->loadXML($xml);
    
    if (!$dom) {
        throw new Exception("❌ XML inválido gerado");
    }
    
    echo "   ✅ XML válido gerado (" . strlen($xml) . " bytes)\n";
    
    // 4. Testa elementos específicos do XML
    echo "   ✅ Contém declaração XML\n";
    if (strpos($xml, '<nfeProc') !== false) echo "   ✅ Contém elemento nfeProc\n";
    if (strpos($xml, '<NFe') !== false) echo "   ✅ Contém elemento NFe\n";
    if (strpos($xml, '<protNFe') !== false) echo "   ✅ Contém elemento protNFe\n";
    
    echo "\n";
    
    // 5. Salva XML de teste
    echo "📋 Teste 4: Salvamento do XML\n";
    $outputFile = 'teste_json2xml_output.xml';
    
    if (file_put_contents($outputFile, $xml) === false) {
        throw new Exception("❌ Erro ao salvar arquivo XML");
    }
    
    echo "   ✅ XML salvo em: $outputFile\n\n";
    
    // 6. Estatísticas
    echo "📊 Estatísticas:\n";
    echo "   • Versão: " . $data['versao'] . "\n";
    echo "   • ID NFe: " . $data['nfe']['id'] . "\n";
    echo "   • Quantidade de itens: " . count($data['nfe']['itens']) . "\n";
    
    if (isset($data['nfe']['total']['icms']['valor_total_nf'])) {
        echo "   • Valor total: R$ " . number_format($data['nfe']['total']['icms']['valor_total_nf'], 2, ',', '.') . "\n";
    }
    
    echo "   • Status protocolo: " . $data['protocolo']['codigo_status'] . " - " . $data['protocolo']['motivo'] . "\n";
    echo "   • Tamanho JSON: " . number_format(strlen($jsonContent)) . " bytes\n";
    echo "   • Tamanho XML: " . number_format(strlen($xml)) . " bytes\n\n";
    
    echo "🎉 Todos os testes passaram com sucesso!\n\n";
    
    echo "🌐 Para testar via web:\n";
    echo "   1. Acesse: http://seu-servidor/json2xml\n";
    echo "   2. Faça upload do arquivo procnfe_array.json\n";
    echo "   3. Ou cole o conteúdo JSON no formulário\n";
    echo "   4. Clique em 'Converter JSON para XML'\n";
    echo "   5. O arquivo XML será baixado automaticamente\n\n";
    
} catch (Exception $e) {
    echo "❌ Erro no teste: " . $e->getMessage() . "\n";
    exit(1);
}

/**
 * Gera XML de teste (versão simplificada)
 */
function generateTestXml($data) {
    $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    
    // Namespace do procNfe
    $xml .= '<nfeProc versao="' . $data['versao'] . '" xmlns="http://www.portalfiscal.inf.br/nfe">' . "\n";
    
    // NFe
    $xml .= '  <NFe xmlns="http://www.portalfiscal.inf.br/nfe">' . "\n";
    $xml .= '    <infNFe Id="' . $data['nfe']['id'] . '" versao="' . $data['nfe']['versao'] . '">' . "\n";
    
    // Exemplo de estrutura básica
    $xml .= '      <!-- NFe com ' . count($data['nfe']['itens']) . ' itens -->' . "\n";
    
    if (isset($data['nfe']['total']['icms']['valor_total_nf'])) {
        $xml .= '      <!-- Valor total: R$ ' . $data['nfe']['total']['icms']['valor_total_nf'] . ' -->' . "\n";
    }
    
    // Simulação de alguns elementos obrigatórios
    $xml .= '      <ide>' . "\n";
    $xml .= '        <mod>55</mod>' . "\n";
    $xml .= '        <serie>1</serie>' . "\n";
    $xml .= '        <dhEmi>2025-07-23T17:35:28-03:00</dhEmi>' . "\n";
    $xml .= '      </ide>' . "\n";
    
    $xml .= '    </infNFe>' . "\n";
    $xml .= '  </NFe>' . "\n";
    
    // Protocolo
    $xml .= '  <protNFe versao="' . $data['protocolo']['versao'] . '">' . "\n";
    $xml .= '    <infProt>' . "\n";
    $xml .= '      <tpAmb>' . $data['protocolo']['ambiente'] . '</tpAmb>' . "\n";
    $xml .= '      <verAplic>' . $data['protocolo']['versao_aplicacao'] . '</verAplic>' . "\n";
    $xml .= '      <chNFe>' . $data['protocolo']['chave_nfe'] . '</chNFe>' . "\n";
    $xml .= '      <dhRecbto>' . $data['protocolo']['data_recebimento'] . '</dhRecbto>' . "\n";
    $xml .= '      <nProt>' . $data['protocolo']['numero_protocolo'] . '</nProt>' . "\n";
    $xml .= '      <digVal>' . $data['protocolo']['digest_value'] . '</digVal>' . "\n";
    $xml .= '      <cStat>' . $data['protocolo']['codigo_status'] . '</cStat>' . "\n";
    $xml .= '      <xMotivo>' . htmlspecialchars($data['protocolo']['motivo']) . '</xMotivo>' . "\n";
    $xml .= '    </infProt>' . "\n";
    $xml .= '  </protNFe>' . "\n";
    
    $xml .= '</nfeProc>';
    
    return $xml;
}
