<?php
/**
 * Test script to POST JSON to the standalone json2xml
 */

// Read the JSON file
$jsonFile = '/home/<USER>/environment/Office/Apps/inProduction/metrics/procnfe_array.json';
$jsonContent = file_get_contents($jsonFile);

if (!$jsonContent) {
    die("Could not read JSON file\n");
}

echo "Testing Standalone JSON2XML via POST\n";
echo "====================================\n";
echo "JSON file size: " . strlen($jsonContent) . " bytes\n";

// Test URL - standalone version
$url = 'https://dev.office.internut.com.br/metrics/json2xml_standalone.php';

// Prepare POST data
$postData = array(
    'json_content' => $jsonContent
);

// Initialize cURL
$ch = curl_init();

// Set cURL options
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

// Execute request
echo "\nSending POST request to standalone version...\n";
$response = curl_exec($ch);

// Get request info
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
$error = curl_error($ch);

// Close cURL
curl_close($ch);

echo "\nResponse Details:\n";
echo "HTTP Code: $httpCode\n";
echo "Content Type: $contentType\n";
echo "Response Length: " . strlen($response) . " bytes\n";

if ($error) {
    echo "cURL Error: $error\n";
}

if ($httpCode == 200) {
    echo "\n✅ SUCCESS! Standalone version works\n";
    
    // Check if it's XML
    if (strpos($contentType, 'xml') !== false || strpos($response, '<?xml') === 0) {
        echo "✅ Response is XML - download would work\n";
        echo "XML Preview (first 300 chars):\n";
        echo substr($response, 0, 300) . "...\n";
        
        // Save XML to file for inspection
        file_put_contents('test_output.xml', $response);
        echo "\n✅ XML saved to test_output.xml\n";
    } else {
        echo "Response Preview (first 500 chars):\n";
        echo substr($response, 0, 500) . "...\n";
    }
} else {
    echo "\n❌ ERROR! HTTP Code: $httpCode\n";
    echo "Response:\n";
    echo substr($response, 0, 1000) . "\n";
}

echo "\n=== Test Complete ===\n";
?>
